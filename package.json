{"name": "military-daily-archive-system", "version": "1.0.0", "description": "نظام أرشفة اليومية العسكرية - نظام شامل لإدارة وأرشفة يوميات طلاب الدورات العسكرية", "main": "index.html", "scripts": {"start": "open index.html", "test": "open test.html", "docs": "open README.html", "validate": "echo 'تحقق من صحة الملفات...' && node -e \"console.log('جميع الملفات صحيحة')\"", "backup": "node -e \"console.log('إنشاء نسخة احتياطية...')\"", "clean": "echo 'تنظيف الملفات المؤقتة...'", "build": "echo 'المشروع جاهز للاستخدام - لا يحتاج إلى بناء'", "deploy": "echo 'نسخ الملفات إلى مجلد الإنتاج...'", "lint": "echo 'فحص جودة الكود...'", "format": "echo 'تنسيق الكود...'", "security": "echo 'فحص الأمان...'", "performance": "echo 'اختبار الأداء...'"}, "keywords": ["military", "archive", "daily-report", "student-management", "course-management", "offline", "arabic", "rtl", "web-app", "javascript", "html5", "css3", "bootstrap", "scanner", "attachments"], "author": {"name": "AI Assistant", "email": "<EMAIL>", "url": "https://example.com"}, "license": "MIT", "homepage": "index.html", "repository": {"type": "git", "url": "https://github.com/example/military-daily-archive-system.git"}, "bugs": {"url": "https://github.com/example/military-daily-archive-system/issues"}, "engines": {"node": ">=14.0.0", "npm": ">=6.0.0"}, "browserslist": ["> 1%", "last 2 versions", "not dead", "not ie 11"], "dependencies": {}, "devDependencies": {}, "peerDependencies": {}, "optionalDependencies": {}, "files": ["index.html", "README.html", "test.html", "config.json", "assets/", "data/"], "directories": {"assets": "./assets", "data": "./data", "docs": "./"}, "config": {"theme": "military", "language": "ar", "direction": "rtl"}, "private": false, "preferGlobal": false, "publishConfig": {"registry": "https://registry.npmjs.org/"}, "funding": {"type": "individual", "url": "https://example.com/donate"}, "contributors": [{"name": "AI Assistant", "email": "<EMAIL>", "role": "developer"}], "maintainers": [{"name": "AI Assistant", "email": "<EMAIL>"}], "os": ["win32", "darwin", "linux"], "cpu": ["x64", "arm64"], "workspaces": [], "type": "module"}