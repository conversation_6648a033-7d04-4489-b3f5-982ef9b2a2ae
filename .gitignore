# نظام أرشفة اليومية العسكرية - ملف .gitignore

# ===== ملفات النظام =====
.DS_Store
.DS_Store?
._*
.Spotlight-V100
.Trashes
ehthumbs.db
Thumbs.db
desktop.ini

# ===== ملفات المحررات =====
# Visual Studio Code
.vscode/
*.code-workspace

# Sublime Text
*.sublime-project
*.sublime-workspace

# Atom
.atom/

# Vim
*.swp
*.swo
*~

# Emacs
*~
\#*\#
/.emacs.desktop
/.emacs.desktop.lock
*.elc
auto-save-list
tramp
.\#*

# ===== ملفات Node.js (إذا تم استخدامها لاحقاً) =====
node_modules/
npm-debug.log*
yarn-debug.log*
yarn-error.log*
lerna-debug.log*
.pnpm-debug.log*

# Runtime data
pids
*.pid
*.seed
*.pid.lock

# Coverage directory used by tools like istanbul
coverage/
*.lcov

# nyc test coverage
.nyc_output

# Grunt intermediate storage
.grunt

# Bower dependency directory
bower_components

# node-waf configuration
.lock-wscript

# Compiled binary addons
build/Release

# Dependency directories
jspm_packages/

# TypeScript cache
*.tsbuildinfo

# Optional npm cache directory
.npm

# Optional eslint cache
.eslintcache

# Optional stylelint cache
.stylelintcache

# Microbundle cache
.rpt2_cache/
.rts2_cache_cjs/
.rts2_cache_es/
.rts2_cache_umd/

# Optional REPL history
.node_repl_history

# Output of 'npm pack'
*.tgz

# Yarn Integrity file
.yarn-integrity

# dotenv environment variable files
.env
.env.development.local
.env.test.local
.env.production.local
.env.local

# parcel-bundler cache
.cache
.parcel-cache

# Next.js build output
.next
out

# Nuxt.js build / generate output
.nuxt
dist

# Gatsby files
.cache/
public

# Storybook build outputs
.out
.storybook-out
storybook-static

# Temporary folders
tmp/
temp/

# ===== ملفات البيانات الحساسة =====
# بيانات المستخدمين الحقيقية
/data/real_data.json
/data/production_data.json
/data/live_data.json

# ملفات النسخ الاحتياطية
/backups/
*.backup
*.bak

# ملفات السجلات
/logs/
*.log

# ملفات التكوين الحساسة
config.local.json
settings.local.json
.env.local

# ===== ملفات المرفقات المؤقتة =====
/uploads/temp/
/attachments/temp/
/scanned/temp/

# ملفات الصور المؤقتة
*.tmp.jpg
*.tmp.png
*.tmp.pdf

# ===== ملفات التطوير =====
# ملفات الاختبار المؤقتة
/test/temp/
/test/fixtures/temp/

# ملفات التوثيق المؤقتة
/docs/temp/
*.draft.md

# ملفات البناء
/build/
/dist/

# ===== ملفات IDE أخرى =====
# IntelliJ IDEA
.idea/
*.iml
*.iws

# Eclipse
.project
.metadata
bin/
tmp/
*.tmp
*.bak
*.swp
*~.nib
local.properties
.settings/
.loadpath
.recommenders

# NetBeans
/nbproject/private/
/nbbuild/
/dist/
/nbdist/
/.nb-gradle/

# ===== ملفات أخرى =====
# ملفات الضغط
*.7z
*.dmg
*.gz
*.iso
*.jar
*.rar
*.tar
*.zip

# ملفات قواعد البيانات المحلية
*.sqlite
*.sqlite3
*.db

# ملفات التخزين المؤقت
.cache/
*.cache

# ملفات الإحصائيات
stats.json
analytics.json

# ملفات التقارير
/reports/temp/
*.report.html
*.report.pdf

# ===== ملفات خاصة بالمشروع =====
# ملفات التكوين المحلية
config.development.json
config.production.json

# ملفات البيانات التجريبية الكبيرة
/data/large_test_data.json
/data/performance_test_data.json

# ملفات المرفقات الكبيرة
/attachments/large_files/
*.large.pdf
*.large.jpg

# ملفات النسخ الاحتياطية التلقائية
auto_backup_*.json
daily_backup_*.json

# ===== ملفات النشر =====
# ملفات التوزيع
/release/
/package/
*.release.zip

# ملفات التوثيق المولدة
/docs/generated/
api-docs/

# ===== ملاحظات =====
# هذا الملف يحمي البيانات الحساسة ويحافظ على نظافة المستودع
# يمكن تعديل هذا الملف حسب احتياجات المشروع المحددة
