/**
 * نظام أرشفة اليومية العسكرية - ملف JavaScript الرئيسي
 * يحتوي على جميع الوظائف الأساسية للنظام
 */

// ===== المتغيرات العامة =====
let currentUser = 'المشرف الرئيسي';
let currentSection = 'dashboard';
let courses = [];
let students = [];
let dailyReports = [];
let users = [];

// ===== تهيئة النظام عند تحميل الصفحة =====
document.addEventListener('DOMContentLoaded', function() {
    initializeSystem();
});

/**
 * تهيئة النظام الأساسية
 */
function initializeSystem() {
    // إخفاء شاشة التحميل بعد 2 ثانية
    setTimeout(() => {
        const loadingScreen = document.getElementById('loadingScreen');
        loadingScreen.classList.add('hidden');
    }, 2000);

    // تحديث الوقت الحالي
    updateCurrentTime();
    setInterval(updateCurrentTime, 1000);

    // تحميل البيانات من التخزين المحلي
    loadDataFromStorage();

    // تحديث الإحصائيات
    updateStatistics();

    // تفعيل الأحداث
    setupEventListeners();

    // تفعيل السحب والإفلات
    setupDragAndDrop();

    console.log('تم تهيئة النظام بنجاح');
}

/**
 * تحديث الوقت الحالي
 */
function updateCurrentTime() {
    const now = new Date();
    const timeString = now.toLocaleString('ar-SA', {
        year: 'numeric',
        month: '2-digit',
        day: '2-digit',
        hour: '2-digit',
        minute: '2-digit',
        second: '2-digit'
    });
    
    const timeElement = document.getElementById('currentTime');
    if (timeElement) {
        timeElement.textContent = timeString;
    }
}

/**
 * تحميل البيانات من التخزين المحلي
 */
function loadDataFromStorage() {
    try {
        // تحميل الدورات
        const storedCourses = localStorage.getItem('military_courses');
        if (storedCourses) {
            courses = JSON.parse(storedCourses);
        }

        // تحميل الطلاب
        const storedStudents = localStorage.getItem('military_students');
        if (storedStudents) {
            students = JSON.parse(storedStudents);
        }

        // تحميل اليوميات
        const storedReports = localStorage.getItem('military_reports');
        if (storedReports) {
            dailyReports = JSON.parse(storedReports);
        }

        // تحميل المستخدمين
        const storedUsers = localStorage.getItem('military_users');
        if (storedUsers) {
            users = JSON.parse(storedUsers);
        } else {
            // إنشاء مستخدم افتراضي
            users = [{
                id: 1,
                name: 'المشرف الرئيسي',
                username: 'admin',
                role: 'admin',
                createdAt: new Date().toISOString()
            }];
            saveDataToStorage();
        }

        console.log('تم تحميل البيانات من التخزين المحلي');
    } catch (error) {
        console.error('خطأ في تحميل البيانات:', error);
        showNotification('خطأ في تحميل البيانات', 'error');
    }
}

/**
 * حفظ البيانات في التخزين المحلي
 */
function saveDataToStorage() {
    try {
        localStorage.setItem('military_courses', JSON.stringify(courses));
        localStorage.setItem('military_students', JSON.stringify(students));
        localStorage.setItem('military_reports', JSON.stringify(dailyReports));
        localStorage.setItem('military_users', JSON.stringify(users));
        console.log('تم حفظ البيانات في التخزين المحلي');
    } catch (error) {
        console.error('خطأ في حفظ البيانات:', error);
        showNotification('خطأ في حفظ البيانات', 'error');
    }
}

/**
 * تحديث الإحصائيات في لوحة التحكم
 */
function updateStatistics() {
    // تحديث عدد الدورات
    const totalCoursesElement = document.getElementById('totalCourses');
    if (totalCoursesElement) {
        totalCoursesElement.textContent = courses.length;
    }

    // تحديث عدد الطلاب
    const totalStudentsElement = document.getElementById('totalStudents');
    if (totalStudentsElement) {
        totalStudentsElement.textContent = students.length;
    }

    // تحديث عدد اليوميات
    const totalReportsElement = document.getElementById('totalReports');
    if (totalReportsElement) {
        totalReportsElement.textContent = dailyReports.length;
    }

    // تحديث عدد المخالفات
    const totalViolations = dailyReports.reduce((total, report) => {
        return total + (report.students ? report.students.filter(s => s.violations && s.violations.length > 0).length : 0);
    }, 0);
    
    const totalViolationsElement = document.getElementById('totalViolations');
    if (totalViolationsElement) {
        totalViolationsElement.textContent = totalViolations;
    }
}

/**
 * إعداد مستمعي الأحداث
 */
function setupEventListeners() {
    // تفعيل روابط التنقل
    const navLinks = document.querySelectorAll('.nav-link');
    navLinks.forEach(link => {
        link.addEventListener('click', function(e) {
            e.preventDefault();

            // إزالة الفئة النشطة من جميع الروابط
            navLinks.forEach(l => l.classList.remove('active'));

            // إضافة الفئة النشطة للرابط المحدد
            this.classList.add('active');
        });
    });
}

/**
 * إعداد السحب والإفلات للملفات
 */
function setupDragAndDrop() {
    // منع السلوك الافتراضي للسحب والإفلات في كامل الصفحة
    ['dragenter', 'dragover', 'dragleave', 'drop'].forEach(eventName => {
        document.addEventListener(eventName, preventDefaults, false);
    });

    function preventDefaults(e) {
        e.preventDefault();
        e.stopPropagation();
    }

    // إعداد منطقة السحب والإفلات
    document.addEventListener('DOMContentLoaded', function() {
        const dropZone = document.getElementById('dropZone');
        if (dropZone) {
            ['dragenter', 'dragover'].forEach(eventName => {
                dropZone.addEventListener(eventName, highlight, false);
            });

            ['dragleave', 'drop'].forEach(eventName => {
                dropZone.addEventListener(eventName, unhighlight, false);
            });

            dropZone.addEventListener('drop', handleDrop, false);
        }
    });

    function highlight(e) {
        const dropZone = document.getElementById('dropZone');
        if (dropZone) {
            dropZone.classList.add('dragover');
        }
    }

    function unhighlight(e) {
        const dropZone = document.getElementById('dropZone');
        if (dropZone) {
            dropZone.classList.remove('dragover');
        }
    }

    function handleDrop(e) {
        const dt = e.dataTransfer;
        const files = dt.files;
        handleFileSelect(files);
    }
}

/**
 * عرض قسم معين وإخفاء الأقسام الأخرى
 * @param {string} sectionId - معرف القسم المراد عرضه
 */
function showSection(sectionId) {
    // إخفاء جميع الأقسام
    const sections = document.querySelectorAll('.content-section');
    sections.forEach(section => {
        section.classList.remove('active');
    });

    // عرض القسم المحدد
    const targetSection = document.getElementById(sectionId);
    if (targetSection) {
        targetSection.classList.add('active');
        currentSection = sectionId;

        // تحديث الرابط النشط في شريط التنقل
        updateActiveNavLink(sectionId);

        // تحميل البيانات الخاصة بالقسم
        loadSectionData(sectionId);

        console.log(`تم عرض القسم: ${sectionId}`);
    }
}

/**
 * تحديث الرابط النشط في شريط التنقل
 * @param {string} sectionId - معرف القسم النشط
 */
function updateActiveNavLink(sectionId) {
    const navLinks = document.querySelectorAll('.nav-link');
    navLinks.forEach(link => {
        link.classList.remove('active');

        // البحث عن الرابط المطابق للقسم
        const onclick = link.getAttribute('onclick');
        if (onclick && onclick.includes(sectionId)) {
            link.classList.add('active');
        }
    });
}

/**
 * تحميل البيانات الخاصة بقسم معين
 * @param {string} sectionId - معرف القسم
 */
function loadSectionData(sectionId) {
    switch (sectionId) {
        case 'courses':
            loadCoursesTable();
            updateCoursesStatistics();
            break;
        case 'daily-report':
            initializeDailyReport();
            break;
        case 'dashboard':
            updateStatistics();
            break;
        // يمكن إضافة المزيد من الأقسام هنا
        default:
            break;
    }
}

/**
 * عرض إشعار للمستخدم
 * @param {string} message - نص الإشعار
 * @param {string} type - نوع الإشعار (success, warning, error, info)
 * @param {number} duration - مدة عرض الإشعار بالميلي ثانية (افتراضي: 5000)
 */
function showNotification(message, type = 'info', duration = 5000) {
    const toastContainer = document.getElementById('toastContainer');
    if (!toastContainer) return;

    // إنشاء عنصر الإشعار
    const toastId = 'toast_' + Date.now();
    const toastHTML = `
        <div id="${toastId}" class="toast toast-${type}" role="alert" aria-live="assertive" aria-atomic="true">
            <div class="toast-header">
                <i class="fas fa-${getIconForType(type)} me-2 text-${type}"></i>
                <strong class="me-auto">${getTitleForType(type)}</strong>
                <small class="text-muted">الآن</small>
                <button type="button" class="btn-close" data-bs-dismiss="toast" aria-label="إغلاق"></button>
            </div>
            <div class="toast-body">
                ${message}
            </div>
        </div>
    `;

    // إضافة الإشعار إلى الحاوية
    toastContainer.insertAdjacentHTML('beforeend', toastHTML);

    // تفعيل الإشعار
    const toastElement = document.getElementById(toastId);
    const toast = new bootstrap.Toast(toastElement, {
        autohide: true,
        delay: duration
    });

    toast.show();

    // إزالة الإشعار من DOM بعد إخفائه
    toastElement.addEventListener('hidden.bs.toast', function() {
        toastElement.remove();
    });
}

/**
 * الحصول على أيقونة مناسبة لنوع الإشعار
 * @param {string} type - نوع الإشعار
 * @returns {string} - اسم الأيقونة
 */
function getIconForType(type) {
    const icons = {
        success: 'check-circle',
        warning: 'exclamation-triangle',
        error: 'times-circle',
        info: 'info-circle'
    };
    return icons[type] || 'info-circle';
}

/**
 * الحصول على عنوان مناسب لنوع الإشعار
 * @param {string} type - نوع الإشعار
 * @returns {string} - عنوان الإشعار
 */
function getTitleForType(type) {
    const titles = {
        success: 'نجح',
        warning: 'تحذير',
        error: 'خطأ',
        info: 'معلومات'
    };
    return titles[type] || 'إشعار';
}

/**
 * تصدير البيانات إلى ملف JSON
 */
function exportData() {
    try {
        const data = {
            courses: courses,
            students: students,
            dailyReports: dailyReports,
            users: users,
            exportDate: new Date().toISOString()
        };

        const dataStr = JSON.stringify(data, null, 2);
        const dataBlob = new Blob([dataStr], { type: 'application/json' });
        
        const link = document.createElement('a');
        link.href = URL.createObjectURL(dataBlob);
        link.download = `military_archive_backup_${new Date().toISOString().split('T')[0]}.json`;
        link.click();

        showNotification('تم تصدير البيانات بنجاح', 'success');
    } catch (error) {
        console.error('خطأ في تصدير البيانات:', error);
        showNotification('خطأ في تصدير البيانات', 'error');
    }
}

/**
 * استيراد البيانات من ملف JSON
 * @param {File} file - ملف البيانات
 */
function importData(file) {
    const reader = new FileReader();
    
    reader.onload = function(e) {
        try {
            const data = JSON.parse(e.target.result);
            
            if (data.courses) courses = data.courses;
            if (data.students) students = data.students;
            if (data.dailyReports) dailyReports = data.dailyReports;
            if (data.users) users = data.users;
            
            saveDataToStorage();
            updateStatistics();
            
            showNotification('تم استيراد البيانات بنجاح', 'success');
        } catch (error) {
            console.error('خطأ في استيراد البيانات:', error);
            showNotification('خطأ في استيراد البيانات - تأكد من صحة الملف', 'error');
        }
    };
    
    reader.readAsText(file);
}

/**
 * عرض نافذة إضافة دورة جديدة
 */
function showAddCourseModal() {
    // إعادة تعيين النموذج
    document.getElementById('addCourseForm').reset();

    // عرض النافذة
    const modal = new bootstrap.Modal(document.getElementById('addCourseModal'));
    modal.show();
}

/**
 * حفظ دورة جديدة
 */
function saveCourse() {
    const form = document.getElementById('addCourseForm');

    // التحقق من صحة البيانات
    if (!form.checkValidity()) {
        form.reportValidity();
        return;
    }

    // جمع البيانات من النموذج
    const courseData = {
        id: Date.now(), // معرف مؤقت
        courseCode: document.getElementById('courseCode').value.trim(),
        courseNumber: document.getElementById('courseNumber').value.trim(),
        courseName: document.getElementById('courseName').value.trim(),
        startDate: document.getElementById('startDate').value,
        endDate: document.getElementById('endDate').value,
        description: document.getElementById('courseDescription').value.trim(),
        maxStudents: parseInt(document.getElementById('maxStudents').value) || 50,
        status: document.getElementById('courseStatus').value,
        createdAt: new Date().toISOString(),
        updatedAt: new Date().toISOString()
    };

    // التحقق من عدم تكرار رمز الدورة
    const existingCourse = courses.find(c => c.courseCode === courseData.courseCode);
    if (existingCourse) {
        showNotification('رمز الدورة موجود مسبقاً، يرجى اختيار رمز آخر', 'warning');
        return;
    }

    // التحقق من صحة التواريخ
    if (new Date(courseData.startDate) >= new Date(courseData.endDate)) {
        showNotification('تاريخ البداية يجب أن يكون قبل تاريخ النهاية', 'warning');
        return;
    }

    try {
        // إضافة الدورة إلى المصفوفة
        courses.push(courseData);

        // حفظ البيانات
        saveDataToStorage();

        // تحديث الجدول والإحصائيات
        loadCoursesTable();
        updateStatistics();
        updateCoursesStatistics();

        // إخفاء النافذة
        const modal = bootstrap.Modal.getInstance(document.getElementById('addCourseModal'));
        modal.hide();

        // عرض رسالة نجاح
        showNotification(`تم إضافة الدورة "${courseData.courseName}" بنجاح`, 'success');

    } catch (error) {
        console.error('خطأ في حفظ الدورة:', error);
        showNotification('حدث خطأ أثناء حفظ الدورة', 'error');
    }
}

/**
 * تحميل جدول الدورات
 */
function loadCoursesTable() {
    const tableBody = document.getElementById('coursesTableBody');
    if (!tableBody) return;

    // مسح المحتوى الحالي
    tableBody.innerHTML = '';

    if (courses.length === 0) {
        tableBody.innerHTML = `
            <tr>
                <td colspan="8" class="text-center text-muted py-4">
                    <i class="fas fa-graduation-cap fa-3x mb-3 d-block"></i>
                    لا توجد دورات مسجلة حالياً
                </td>
            </tr>
        `;
        return;
    }

    // إضافة صفوف الدورات
    courses.forEach(course => {
        const studentsCount = students.filter(s => s.courseCode === course.courseCode).length;
        const statusBadge = getStatusBadge(course.status);

        const row = `
            <tr>
                <td><strong>${course.courseCode}</strong></td>
                <td>${course.courseNumber}</td>
                <td>${course.courseName}</td>
                <td>${formatDate(course.startDate)}</td>
                <td>${formatDate(course.endDate)}</td>
                <td>${statusBadge}</td>
                <td>
                    <span class="badge bg-info">${studentsCount}</span>
                    ${course.maxStudents ? `/ ${course.maxStudents}` : ''}
                </td>
                <td>
                    <div class="btn-group btn-group-sm" role="group">
                        <button class="btn btn-outline-primary" onclick="viewCourse(${course.id})"
                                title="عرض التفاصيل">
                            <i class="fas fa-eye"></i>
                        </button>
                        <button class="btn btn-outline-warning" onclick="editCourse(${course.id})"
                                title="تعديل">
                            <i class="fas fa-edit"></i>
                        </button>
                        <button class="btn btn-outline-danger" onclick="deleteCourse(${course.id})"
                                title="حذف">
                            <i class="fas fa-trash"></i>
                        </button>
                    </div>
                </td>
            </tr>
        `;

        tableBody.insertAdjacentHTML('beforeend', row);
    });
}

/**
 * الحصول على شارة الحالة
 * @param {string} status - حالة الدورة
 * @returns {string} - HTML للشارة
 */
function getStatusBadge(status) {
    const badges = {
        'upcoming': '<span class="badge bg-warning">قادمة</span>',
        'active': '<span class="badge bg-success">نشطة</span>',
        'completed': '<span class="badge bg-secondary">مكتملة</span>',
        'cancelled': '<span class="badge bg-danger">ملغية</span>'
    };
    return badges[status] || '<span class="badge bg-light text-dark">غير محدد</span>';
}

/**
 * تنسيق التاريخ للعرض
 * @param {string} dateString - التاريخ كنص
 * @returns {string} - التاريخ المنسق
 */
function formatDate(dateString) {
    if (!dateString) return '-';

    const date = new Date(dateString);
    return date.toLocaleDateString('ar-SA', {
        year: 'numeric',
        month: '2-digit',
        day: '2-digit'
    });
}

/**
 * تحديث إحصائيات الدورات
 */
function updateCoursesStatistics() {
    const activeCourses = courses.filter(c => c.status === 'active').length;
    const completedCourses = courses.filter(c => c.status === 'completed').length;
    const upcomingCourses = courses.filter(c => c.status === 'upcoming').length;
    const totalEnrolled = students.length;

    // تحديث العناصر في الصفحة
    const activeElement = document.getElementById('activeCourses');
    if (activeElement) activeElement.textContent = activeCourses;

    const completedElement = document.getElementById('completedCourses');
    if (completedElement) completedElement.textContent = completedCourses;

    const upcomingElement = document.getElementById('upcomingCourses');
    if (upcomingElement) upcomingElement.textContent = upcomingCourses;

    const enrolledElement = document.getElementById('totalEnrolled');
    if (enrolledElement) enrolledElement.textContent = totalEnrolled;
}

/**
 * تهيئة نموذج اليومية
 */
function initializeDailyReport() {
    // تعيين التاريخ الحالي
    const today = new Date().toISOString().split('T')[0];
    document.getElementById('reportDate').value = today;

    // تعيين اسم المشرف الحالي
    document.getElementById('supervisorName').value = currentUser;

    // تحميل قائمة الدورات
    loadCoursesDropdown();
}

/**
 * تحميل قائمة الدورات في القائمة المنسدلة
 */
function loadCoursesDropdown() {
    const courseSelect = document.getElementById('selectedCourse');
    const studentCourseSelect = document.getElementById('studentCourse');

    if (courseSelect) {
        courseSelect.innerHTML = '<option value="">اختر الدورة...</option>';
        courses.forEach(course => {
            if (course.status === 'active') {
                const option = `<option value="${course.courseCode}" data-course='${JSON.stringify(course)}'>${course.courseName} (${course.courseNumber})</option>`;
                courseSelect.insertAdjacentHTML('beforeend', option);
            }
        });
    }

    if (studentCourseSelect) {
        studentCourseSelect.innerHTML = '<option value="">اختر الدورة...</option>';
        courses.forEach(course => {
            const option = `<option value="${course.courseCode}" data-course='${JSON.stringify(course)}'>${course.courseName} (${course.courseNumber})</option>`;
            studentCourseSelect.insertAdjacentHTML('beforeend', option);
        });
    }
}

/**
 * تحميل طلاب الدورة المحددة
 */
function loadCourseStudents() {
    const courseSelect = document.getElementById('selectedCourse');
    const selectedOption = courseSelect.options[courseSelect.selectedIndex];

    if (!selectedOption || !selectedOption.value) {
        document.getElementById('studentsContainer').innerHTML = `
            <div class="text-center text-muted py-5">
                <i class="fas fa-users fa-3x mb-3 d-block"></i>
                <p>يرجى اختيار الدورة أولاً لعرض قائمة الطلاب</p>
            </div>
        `;
        return;
    }

    const courseCode = selectedOption.value;
    const courseStudents = students.filter(s => s.courseCode === courseCode);

    if (courseStudents.length === 0) {
        document.getElementById('studentsContainer').innerHTML = `
            <div class="text-center text-muted py-5">
                <i class="fas fa-user-plus fa-3x mb-3 d-block"></i>
                <p>لا يوجد طلاب مسجلين في هذه الدورة</p>
                <button class="btn btn-military-primary mt-2" onclick="showAddStudentModal()">
                    <i class="fas fa-plus me-1"></i>إضافة طالب جديد
                </button>
            </div>
        `;
        return;
    }

    // عرض قائمة الطلاب
    let studentsHTML = '';
    courseStudents.forEach((student, index) => {
        studentsHTML += createStudentCard(student, index);
    });

    document.getElementById('studentsContainer').innerHTML = studentsHTML;
}

/**
 * إنشاء بطاقة طالب
 * @param {Object} student - بيانات الطالب
 * @param {number} index - فهرس الطالب
 * @returns {string} - HTML للبطاقة
 */
function createStudentCard(student, index) {
    return `
        <div class="student-card mb-3" data-student-id="${student.id}">
            <div class="card">
                <div class="card-header bg-light">
                    <div class="row align-items-center">
                        <div class="col-md-8">
                            <h6 class="mb-0">
                                <i class="fas fa-user me-2"></i>
                                <strong>${student.fullName}</strong>
                                <span class="badge bg-secondary ms-2">${student.rank}</span>
                            </h6>
                            <small class="text-muted">الرقم العسكري: ${student.militaryNumber}</small>
                        </div>
                        <div class="col-md-4 text-end">
                            <button class="btn btn-sm btn-outline-danger" onclick="removeStudentFromReport(${student.id})">
                                <i class="fas fa-times"></i>
                            </button>
                        </div>
                    </div>
                </div>
                <div class="card-body">
                    <div class="row">
                        <!-- المخالفات -->
                        <div class="col-md-4 mb-3">
                            <label class="form-label">
                                <i class="fas fa-exclamation-triangle text-danger me-1"></i>المخالفات
                            </label>
                            <select class="form-select form-select-sm mb-2" onchange="addViolation(${student.id}, this.value)">
                                <option value="">اختر نوع المخالفة...</option>
                                <option value="تأخير عن الطابور">تأخير عن الطابور</option>
                                <option value="عدم ارتداء الزي الرسمي">عدم ارتداء الزي الرسمي</option>
                                <option value="عدم تنفيذ الأوامر">عدم تنفيذ الأوامر</option>
                                <option value="سوء السلوك">سوء السلوك</option>
                                <option value="إهمال في الواجبات">إهمال في الواجبات</option>
                                <option value="مخالفة اللوائح">مخالفة اللوائح</option>
                                <option value="عدم الانضباط">عدم الانضباط</option>
                                <option value="أخرى">أخرى</option>
                            </select>
                            <div id="violations_${student.id}" class="violations-list">
                                <!-- قائمة المخالفات -->
                            </div>
                        </div>

                        <!-- التأخيرات -->
                        <div class="col-md-4 mb-3">
                            <label class="form-label">
                                <i class="fas fa-clock text-warning me-1"></i>التأخيرات (حصص)
                            </label>
                            <input type="number" class="form-control form-control-sm"
                                   id="delays_${student.id}" min="0" max="7" value="0"
                                   placeholder="عدد حصص التأخير">
                        </div>

                        <!-- الغيابات -->
                        <div class="col-md-4 mb-3">
                            <label class="form-label">
                                <i class="fas fa-user-times text-info me-1"></i>الغيابات
                            </label>
                            <div class="row">
                                <div class="col-6">
                                    <label class="form-label form-label-sm">بعذر</label>
                                    <input type="number" class="form-control form-control-sm"
                                           id="absenceWithExcuse_${student.id}" min="0" max="7" value="0">
                                </div>
                                <div class="col-6">
                                    <label class="form-label form-label-sm">بدون عذر</label>
                                    <input type="number" class="form-control form-control-sm"
                                           id="absenceWithoutExcuse_${student.id}" min="0" max="7" value="0">
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    `;
}

/**
 * عرض نافذة إضافة طالب جديد
 */
function showAddStudentModal() {
    // إعادة تعيين النموذج
    document.getElementById('addStudentForm').reset();

    // تحميل قائمة الدورات
    loadCoursesDropdown();

    // عرض النافذة
    const modal = new bootstrap.Modal(document.getElementById('addStudentModal'));
    modal.show();
}

/**
 * تحديث معلومات الدورة عند اختيار دورة للطالب
 */
function updateCourseInfo() {
    const courseSelect = document.getElementById('studentCourse');
    const selectedOption = courseSelect.options[courseSelect.selectedIndex];

    if (selectedOption && selectedOption.value) {
        const courseData = JSON.parse(selectedOption.getAttribute('data-course'));
        document.getElementById('studentCourseNumber').value = courseData.courseNumber;
        document.getElementById('studentCourseName').value = courseData.courseName;
    } else {
        document.getElementById('studentCourseNumber').value = '';
        document.getElementById('studentCourseName').value = '';
    }
}

/**
 * عرض نافذة إضافة مرفق
 */
function showAttachmentModal() {
    // إعادة تعيين النموذج
    document.getElementById('attachmentForm').reset();
    document.getElementById('previewArea').style.display = 'none';

    // تحميل قائمة الطلاب
    loadStudentsForAttachment();

    // تعيين التاريخ الحالي
    document.getElementById('attachmentDate').value = new Date().toISOString().split('T')[0];

    // عرض النافذة
    const modal = new bootstrap.Modal(document.getElementById('attachmentModal'));
    modal.show();
}

/**
 * تحميل قائمة الطلاب للمرفقات
 */
function loadStudentsForAttachment() {
    const studentSelect = document.getElementById('relatedStudent');
    studentSelect.innerHTML = '<option value="">اختر الطالب...</option>';

    students.forEach(student => {
        const option = `<option value="${student.id}">${student.fullName} (${student.militaryNumber})</option>`;
        studentSelect.insertAdjacentHTML('beforeend', option);
    });
}

/**
 * معالجة اختيار الملفات
 * @param {FileList} files - قائمة الملفات المحددة
 */
function handleFileSelect(files) {
    if (files.length === 0) return;

    const file = files[0];

    // التحقق من حجم الملف (10 ميجابايت كحد أقصى)
    if (file.size > 10 * 1024 * 1024) {
        showNotification('حجم الملف كبير جداً. الحد الأقصى 10 ميجابايت', 'warning');
        return;
    }

    // التحقق من نوع الملف
    const allowedTypes = ['application/pdf', 'image/jpeg', 'image/jpg', 'image/png',
                         'application/msword', 'application/vnd.openxmlformats-officedocument.wordprocessingml.document'];

    if (!allowedTypes.includes(file.type)) {
        showNotification('نوع الملف غير مدعوم. يرجى اختيار PDF, JPG, PNG, DOC, أو DOCX', 'warning');
        return;
    }

    // تعيين اسم المرفق تلقائياً
    if (!document.getElementById('attachmentName').value) {
        document.getElementById('attachmentName').value = file.name.split('.')[0];
    }

    // عرض معاينة الملف
    showFilePreview(file);
}

/**
 * عرض معاينة الملف
 * @param {File} file - الملف المراد معاينته
 */
function showFilePreview(file) {
    const previewArea = document.getElementById('previewArea');
    const previewContent = document.getElementById('previewContent');

    previewArea.style.display = 'block';

    if (file.type.startsWith('image/')) {
        // معاينة الصور
        const reader = new FileReader();
        reader.onload = function(e) {
            previewContent.innerHTML = `
                <img src="${e.target.result}" class="img-fluid" style="max-height: 300px; border-radius: 8px;">
                <p class="mt-2 text-muted">${file.name} (${formatFileSize(file.size)})</p>
            `;
        };
        reader.readAsDataURL(file);
    } else if (file.type === 'application/pdf') {
        // معاينة PDF
        previewContent.innerHTML = `
            <div class="text-center">
                <i class="fas fa-file-pdf fa-5x text-danger mb-3"></i>
                <p class="mb-1"><strong>${file.name}</strong></p>
                <p class="text-muted">${formatFileSize(file.size)}</p>
            </div>
        `;
    } else {
        // معاينة الملفات الأخرى
        previewContent.innerHTML = `
            <div class="text-center">
                <i class="fas fa-file-alt fa-5x text-primary mb-3"></i>
                <p class="mb-1"><strong>${file.name}</strong></p>
                <p class="text-muted">${formatFileSize(file.size)}</p>
            </div>
        `;
    }

    // حفظ الملف مؤقتاً
    window.currentAttachmentFile = file;
}

/**
 * تنسيق حجم الملف
 * @param {number} bytes - حجم الملف بالبايت
 * @returns {string} - حجم الملف المنسق
 */
function formatFileSize(bytes) {
    if (bytes === 0) return '0 بايت';

    const k = 1024;
    const sizes = ['بايت', 'كيلوبايت', 'ميجابايت', 'جيجابايت'];
    const i = Math.floor(Math.log(bytes) / Math.log(k));

    return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i];
}

/**
 * بدء المسح الضوئي بالكاميرا
 */
function startWebcamScan() {
    if (window.scanner) {
        window.scanner.startCameraScan();
    } else {
        showNotification('نظام المسح الضوئي غير متاح', 'error');
    }
}

/**
 * بدء المسح الضوئي بالماسح الضوئي
 */
function startScannerScan() {
    if (window.scanner) {
        window.scanner.startScannerScan();
    } else {
        showNotification('نظام المسح الضوئي غير متاح', 'error');
    }
}

/**
 * حفظ المرفق
 */
function saveAttachment() {
    const form = document.getElementById('attachmentForm');

    // التحقق من صحة البيانات
    if (!form.checkValidity()) {
        form.reportValidity();
        return;
    }

    if (!window.currentAttachmentFile) {
        showNotification('يرجى اختيار ملف أولاً', 'warning');
        return;
    }

    // جمع بيانات المرفق
    const attachmentData = {
        id: Date.now(),
        name: document.getElementById('attachmentName').value.trim(),
        type: document.getElementById('attachmentType').value,
        description: document.getElementById('attachmentDescription').value.trim(),
        relatedStudentId: document.getElementById('relatedStudent').value || null,
        date: document.getElementById('attachmentDate').value,
        fileName: window.currentAttachmentFile.name,
        fileSize: window.currentAttachmentFile.size,
        fileType: window.currentAttachmentFile.type,
        uploadDate: new Date().toISOString()
    };

    try {
        // حفظ الملف في التخزين المحلي (كـ base64)
        const reader = new FileReader();
        reader.onload = function(e) {
            attachmentData.fileData = e.target.result;

            // إضافة المرفق إلى قائمة المرفقات
            if (!window.currentReportAttachments) {
                window.currentReportAttachments = [];
            }
            window.currentReportAttachments.push(attachmentData);

            // تحديث عرض المرفقات
            updateAttachmentsDisplay();

            // إخفاء النافذة
            const modal = bootstrap.Modal.getInstance(document.getElementById('attachmentModal'));
            modal.hide();

            // عرض رسالة نجاح
            showNotification(`تم إضافة المرفق "${attachmentData.name}" بنجاح`, 'success');

            // مسح الملف المؤقت
            delete window.currentAttachmentFile;
        };
        reader.readAsDataURL(window.currentAttachmentFile);

    } catch (error) {
        console.error('خطأ في حفظ المرفق:', error);
        showNotification('حدث خطأ أثناء حفظ المرفق', 'error');
    }
}

/**
 * تحديث عرض المرفقات
 */
function updateAttachmentsDisplay() {
    const container = document.getElementById('attachmentsContainer');

    if (!window.currentReportAttachments || window.currentReportAttachments.length === 0) {
        container.innerHTML = `
            <div class="text-center text-muted py-4">
                <i class="fas fa-file-upload fa-2x mb-2 d-block"></i>
                <p>لا توجد مرفقات حالياً</p>
            </div>
        `;
        return;
    }

    let attachmentsHTML = '<div class="row">';

    window.currentReportAttachments.forEach((attachment, index) => {
        attachmentsHTML += `
            <div class="col-md-4 mb-3">
                <div class="attachment-item">
                    <div class="attachment-preview">
                        ${getAttachmentIcon(attachment.fileType)}
                    </div>
                    <div class="attachment-info">
                        <strong>${attachment.name}</strong><br>
                        <small class="text-muted">${attachment.fileName}</small><br>
                        <small class="text-muted">${formatFileSize(attachment.fileSize)}</small>
                    </div>
                    <div class="attachment-actions">
                        <button class="btn btn-sm btn-outline-primary" onclick="viewAttachment(${index})">
                            <i class="fas fa-eye"></i>
                        </button>
                        <button class="btn btn-sm btn-outline-danger" onclick="removeAttachment(${index})">
                            <i class="fas fa-trash"></i>
                        </button>
                    </div>
                </div>
            </div>
        `;
    });

    attachmentsHTML += '</div>';
    container.innerHTML = attachmentsHTML;
}

/**
 * الحصول على أيقونة المرفق حسب نوع الملف
 * @param {string} fileType - نوع الملف
 * @returns {string} - HTML للأيقونة
 */
function getAttachmentIcon(fileType) {
    if (fileType.startsWith('image/')) {
        return '<i class="fas fa-image fa-3x text-success"></i>';
    } else if (fileType === 'application/pdf') {
        return '<i class="fas fa-file-pdf fa-3x text-danger"></i>';
    } else if (fileType.includes('word')) {
        return '<i class="fas fa-file-word fa-3x text-primary"></i>';
    } else {
        return '<i class="fas fa-file fa-3x text-secondary"></i>';
    }
}

// ===== تصدير الوظائف للاستخدام العام =====
window.showSection = showSection;
window.showNotification = showNotification;
window.exportData = exportData;
window.importData = importData;
window.showAddCourseModal = showAddCourseModal;
window.saveCourse = saveCourse;
window.loadCourseStudents = loadCourseStudents;
window.showAddStudentModal = showAddStudentModal;
window.updateCourseInfo = updateCourseInfo;
window.showAttachmentModal = showAttachmentModal;
window.handleFileSelect = handleFileSelect;
window.startWebcamScan = startWebcamScan;
window.startScannerScan = startScannerScan;
window.saveAttachment = saveAttachment;
