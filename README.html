<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>دليل المستخدم - نظام أرشفة اليومية العسكرية</title>
    
    <!-- Bootstrap RTL CSS -->
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.rtl.min.css" rel="stylesheet">
    
    <!-- Font Awesome Icons -->
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css" rel="stylesheet">
    
    <!-- Google Fonts - Arabic -->
    <link href="https://fonts.googleapis.com/css2?family=Cairo:wght@300;400;600;700&display=swap" rel="stylesheet">
    
    <style>
        :root {
            --military-primary: #1a365d;
            --military-secondary: #2d3748;
            --military-accent: #d69e2e;
            --military-success: #38a169;
            --military-light: #f7fafc;
            --military-dark: #1a202c;
            --gradient-primary: linear-gradient(135deg, var(--military-primary) 0%, var(--military-secondary) 100%);
            --gradient-accent: linear-gradient(135deg, var(--military-accent) 0%, #f6ad55 100%);
            --shadow-light: 0 2px 4px rgba(0,0,0,0.1);
            --shadow-medium: 0 4px 8px rgba(0,0,0,0.15);
            --shadow-heavy: 0 8px 16px rgba(0,0,0,0.2);
            --border-radius: 8px;
            --transition: all 0.3s ease;
        }

        * {
            font-family: 'Cairo', 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
        }

        body {
            background: linear-gradient(135deg, #f7fafc 0%, #edf2f7 100%);
            color: var(--military-dark);
            line-height: 1.6;
        }

        .hero-section {
            background: var(--gradient-primary);
            color: white;
            padding: 4rem 0;
            text-align: center;
            position: relative;
            overflow: hidden;
        }

        .hero-section::before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            bottom: 0;
            background: url('data:image/svg+xml,<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 100 100"><defs><pattern id="grain" width="100" height="100" patternUnits="userSpaceOnUse"><circle cx="50" cy="50" r="1" fill="rgba(255,255,255,0.05)"/></pattern></defs><rect width="100" height="100" fill="url(%23grain)"/></svg>');
            opacity: 0.3;
        }

        .hero-content {
            position: relative;
            z-index: 1;
        }

        .hero-logo {
            font-size: 5rem;
            color: var(--military-accent);
            margin-bottom: 1rem;
            animation: pulse 2s infinite;
        }

        @keyframes pulse {
            0%, 100% { transform: scale(1); }
            50% { transform: scale(1.05); }
        }

        .section-card {
            background: white;
            border-radius: var(--border-radius);
            padding: 2rem;
            margin-bottom: 2rem;
            box-shadow: var(--shadow-light);
            border-left: 4px solid var(--military-accent);
            transition: var(--transition);
        }

        .section-card:hover {
            transform: translateY(-5px);
            box-shadow: var(--shadow-heavy);
        }

        .section-title {
            color: var(--military-primary);
            font-weight: 700;
            margin-bottom: 1rem;
            display: flex;
            align-items: center;
            gap: 0.5rem;
        }

        .feature-list {
            list-style: none;
            padding: 0;
        }

        .feature-list li {
            padding: 0.5rem 0;
            border-bottom: 1px solid #e9ecef;
            display: flex;
            align-items: center;
            gap: 0.5rem;
        }

        .feature-list li:last-child {
            border-bottom: none;
        }

        .feature-list i {
            color: var(--military-success);
            width: 20px;
        }

        .step-card {
            background: white;
            border-radius: var(--border-radius);
            padding: 1.5rem;
            margin-bottom: 1rem;
            box-shadow: var(--shadow-light);
            border-top: 4px solid var(--military-primary);
        }

        .step-number {
            background: var(--gradient-accent);
            color: white;
            width: 40px;
            height: 40px;
            border-radius: 50%;
            display: flex;
            align-items: center;
            justify-content: center;
            font-weight: 700;
            margin-bottom: 1rem;
        }

        .btn-military {
            background: var(--gradient-primary);
            border: none;
            color: white;
            font-weight: 600;
            padding: 0.75rem 2rem;
            border-radius: var(--border-radius);
            transition: var(--transition);
            box-shadow: var(--shadow-light);
            text-decoration: none;
            display: inline-block;
        }

        .btn-military:hover {
            background: var(--gradient-accent);
            transform: translateY(-2px);
            box-shadow: var(--shadow-medium);
            color: white;
            text-decoration: none;
        }

        .alert-military {
            background: linear-gradient(135deg, rgba(26, 54, 93, 0.1) 0%, rgba(45, 55, 72, 0.1) 100%);
            border: 1px solid var(--military-primary);
            border-radius: var(--border-radius);
            color: var(--military-dark);
        }

        .tech-badge {
            background: var(--military-light);
            color: var(--military-dark);
            padding: 0.25rem 0.75rem;
            border-radius: 20px;
            font-size: 0.875rem;
            margin: 0.25rem;
            display: inline-block;
            border: 1px solid #dee2e6;
        }

        .footer {
            background: var(--military-dark);
            color: white;
            text-align: center;
            padding: 2rem 0;
            margin-top: 3rem;
        }

        .container {
            max-width: 1200px;
        }

        @media (max-width: 768px) {
            .hero-section {
                padding: 2rem 0;
            }
            
            .hero-logo {
                font-size: 3rem;
            }
            
            .section-card {
                padding: 1.5rem;
            }
        }
    </style>
</head>
<body>
    <!-- Hero Section -->
    <section class="hero-section">
        <div class="container">
            <div class="hero-content">
                <div class="hero-logo">
                    <i class="fas fa-shield-alt"></i>
                </div>
                <h1 class="display-4 mb-3">نظام أرشفة اليومية العسكرية</h1>
                <p class="lead">دليل المستخدم الشامل للنظام الإلكتروني لإدارة وأرشفة يوميات طلاب الدورات العسكرية</p>
                <div class="mt-4">
                    <a href="index.html" class="btn-military">
                        <i class="fas fa-rocket me-2"></i>تشغيل النظام
                    </a>
                </div>
            </div>
        </div>
    </section>

    <!-- Main Content -->
    <div class="container my-5">
        <!-- Overview Section -->
        <div class="section-card">
            <h2 class="section-title">
                <i class="fas fa-info-circle"></i>نظرة عامة على النظام
            </h2>
            <p class="mb-4">
                نظام أرشفة اليومية العسكرية هو تطبيق ويب متقدم مصمم خصيصاً لإدارة وأرشفة يوميات طلاب الدورات العسكرية. 
                يعمل النظام بشكل كامل دون الحاجة لاتصال بالإنترنت ويحفظ جميع البيانات محلياً على جهاز الكمبيوتر.
            </p>
            
            <div class="row">
                <div class="col-md-6">
                    <h5 class="text-military-primary mb-3">
                        <i class="fas fa-star me-2"></i>المميزات الرئيسية
                    </h5>
                    <ul class="feature-list">
                        <li><i class="fas fa-check"></i>واجهة عربية كاملة مع دعم RTL</li>
                        <li><i class="fas fa-check"></i>تصميم عسكري فخم واحترافي</li>
                        <li><i class="fas fa-check"></i>عمل بدون إنترنت (Offline)</li>
                        <li><i class="fas fa-check"></i>حفظ البيانات محلياً</li>
                        <li><i class="fas fa-check"></i>إدارة شاملة للدورات والطلاب</li>
                        <li><i class="fas fa-check"></i>تسجيل المخالفات والغيابات</li>
                        <li><i class="fas fa-check"></i>رفع المرفقات والمسح الضوئي</li>
                        <li><i class="fas fa-check"></i>تقارير وإحصائيات تفصيلية</li>
                    </ul>
                </div>
                <div class="col-md-6">
                    <h5 class="text-military-primary mb-3">
                        <i class="fas fa-cogs me-2"></i>التقنيات المستخدمة
                    </h5>
                    <div class="mb-3">
                        <span class="tech-badge">HTML5</span>
                        <span class="tech-badge">CSS3</span>
                        <span class="tech-badge">JavaScript ES6</span>
                        <span class="tech-badge">Bootstrap 5</span>
                        <span class="tech-badge">Font Awesome</span>
                        <span class="tech-badge">LocalStorage</span>
                        <span class="tech-badge">IndexedDB</span>
                        <span class="tech-badge">Web APIs</span>
                    </div>
                </div>
            </div>
        </div>

        <!-- Installation Section -->
        <div class="section-card">
            <h2 class="section-title">
                <i class="fas fa-download"></i>طريقة التشغيل
            </h2>
            
            <div class="alert alert-military mb-4">
                <h5 class="alert-heading">
                    <i class="fas fa-exclamation-circle me-2"></i>متطلبات النظام
                </h5>
                <ul class="mb-0">
                    <li>متصفح ويب حديث (Chrome, Firefox, Edge, Safari)</li>
                    <li>نظام تشغيل Windows, macOS, أو Linux</li>
                    <li>مساحة تخزين: 50 ميجابايت على الأقل</li>
                    <li>ذاكرة وصول عشوائي: 2 جيجابايت على الأقل</li>
                </ul>
            </div>

            <div class="row">
                <div class="col-md-4">
                    <div class="step-card">
                        <div class="step-number">1</div>
                        <h5>تحميل الملفات</h5>
                        <p>قم بتحميل جميع ملفات النظام في مجلد واحد على جهاز الكمبيوتر.</p>
                    </div>
                </div>
                <div class="col-md-4">
                    <div class="step-card">
                        <div class="step-number">2</div>
                        <h5>فتح الملف الرئيسي</h5>
                        <p>اضغط مرتين على ملف <code>index.html</code> لفتحه في المتصفح.</p>
                    </div>
                </div>
                <div class="col-md-4">
                    <div class="step-card">
                        <div class="step-number">3</div>
                        <h5>بدء الاستخدام</h5>
                        <p>ابدأ بإضافة الدورات والطلاب ثم قم بتسجيل اليوميات.</p>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Footer -->
    <footer class="footer">
        <div class="container">
            <p class="mb-2">
                <i class="fas fa-shield-alt me-2"></i>
                نظام أرشفة اليومية العسكرية - الإصدار 1.0.0
            </p>
            <p class="mb-0">
                <small>تم التطوير بواسطة الذكاء الاصطناعي المتقدم | جميع الحقوق محفوظة © 2024</small>
            </p>
        </div>
    </footer>

    <!-- Bootstrap JS -->
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
</body>
</html>
