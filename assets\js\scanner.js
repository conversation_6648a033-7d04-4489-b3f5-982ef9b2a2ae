/**
 * نظام المسح الضوئي المتقدم
 * يدعم المسح عبر الكاميرا والماسحات الضوئية المتصلة
 */

class AdvancedScanner {
    constructor() {
        this.isScanning = false;
        this.currentStream = null;
        this.scannerDevices = [];
        this.initializeScanner();
    }

    /**
     * تهيئة نظام المسح الضوئي
     */
    async initializeScanner() {
        try {
            // البحث عن الماسحات الضوئية المتاحة
            await this.detectScanners();
            
            // تحديث حالة الماسحات في الواجهة
            this.updateScannerStatus();
            
            console.log('تم تهيئة نظام المسح الضوئي بنجاح');
        } catch (error) {
            console.error('خطأ في تهيئة نظام المسح الضوئي:', error);
        }
    }

    /**
     * البحث عن الماسحات الضوئية المتاحة
     */
    async detectScanners() {
        this.scannerDevices = [];

        try {
            // البحث عن أجهزة USB
            if ('usb' in navigator) {
                const devices = await navigator.usb.getDevices();
                const scanners = devices.filter(device => this.isScannerDevice(device));
                this.scannerDevices.push(...scanners.map(device => ({
                    type: 'usb',
                    device: device,
                    name: device.productName || 'ماسح ضوئي USB'
                })));
            }

            // البحث عن أجهزة الشبكة (إذا كانت متاحة)
            if ('navigator' in window && 'serviceWorker' in navigator) {
                // يمكن إضافة دعم للماسحات الشبكية هنا
            }

        } catch (error) {
            console.warn('فشل في البحث عن الماسحات الضوئية:', error);
        }
    }

    /**
     * التحقق من كون الجهاز ماسح ضوئي
     * @param {USBDevice} device - جهاز USB
     * @returns {boolean} - true إذا كان ماسح ضوئي
     */
    isScannerDevice(device) {
        // فئات الأجهزة المعروفة للماسحات الضوئية
        const scannerClasses = [7, 9]; // Printer, Hub classes
        
        // أسماء المنتجات المعروفة
        const scannerKeywords = ['scanner', 'scan', 'epson', 'canon', 'hp', 'brother'];
        
        if (scannerClasses.includes(device.deviceClass)) {
            return true;
        }
        
        if (device.productName) {
            const productName = device.productName.toLowerCase();
            return scannerKeywords.some(keyword => productName.includes(keyword));
        }
        
        return false;
    }

    /**
     * تحديث حالة الماسحات في الواجهة
     */
    updateScannerStatus() {
        const statusElement = document.getElementById('scannerStatus');
        if (!statusElement) return;

        if (this.scannerDevices.length > 0) {
            statusElement.innerHTML = `
                <div class="text-success">
                    <i class="fas fa-check-circle me-1"></i>
                    تم العثور على ${this.scannerDevices.length} ماسح ضوئي
                </div>
                <small class="text-muted d-block mt-1">
                    ${this.scannerDevices.map(s => s.name).join(', ')}
                </small>
            `;
        } else {
            statusElement.innerHTML = `
                <div class="text-warning">
                    <i class="fas fa-exclamation-triangle me-1"></i>
                    لم يتم العثور على ماسحات ضوئية
                </div>
                <small class="text-muted d-block mt-1">
                    تأكد من توصيل الماسح الضوئي وتشغيله
                </small>
            `;
        }
    }

    /**
     * بدء المسح بالكاميرا
     */
    async startCameraScan() {
        try {
            if (this.isScanning) {
                this.stopScan();
                return;
            }

            // طلب الوصول للكاميرا
            const stream = await navigator.mediaDevices.getUserMedia({
                video: {
                    width: { ideal: 1920 },
                    height: { ideal: 1080 },
                    facingMode: 'environment' // الكاميرا الخلفية إذا كانت متاحة
                }
            });

            this.currentStream = stream;
            this.isScanning = true;

            // إنشاء واجهة التقاط الصور
            this.createCameraInterface(stream);

            showNotification('تم تفعيل الكاميرا بنجاح', 'success');

        } catch (error) {
            console.error('خطأ في تفعيل الكاميرا:', error);
            showNotification('فشل في الوصول للكاميرا. تأكد من منح الإذن للموقع', 'error');
        }
    }

    /**
     * إنشاء واجهة التقاط الصور
     * @param {MediaStream} stream - تدفق الكاميرا
     */
    createCameraInterface(stream) {
        const previewContent = document.getElementById('previewContent');
        if (!previewContent) return;

        previewContent.innerHTML = `
            <div class="camera-interface">
                <video id="cameraVideo" autoplay playsinline style="width: 100%; max-width: 500px; border-radius: 8px;"></video>
                <canvas id="captureCanvas" style="display: none;"></canvas>
                <div class="camera-controls mt-3">
                    <button class="btn btn-military-primary me-2" onclick="scanner.capturePhoto()">
                        <i class="fas fa-camera me-1"></i>التقاط صورة
                    </button>
                    <button class="btn btn-secondary" onclick="scanner.stopScan()">
                        <i class="fas fa-stop me-1"></i>إيقاف
                    </button>
                </div>
            </div>
        `;

        const video = document.getElementById('cameraVideo');
        video.srcObject = stream;

        // عرض منطقة المعاينة
        document.getElementById('previewArea').style.display = 'block';
    }

    /**
     * التقاط صورة من الكاميرا
     */
    capturePhoto() {
        const video = document.getElementById('cameraVideo');
        const canvas = document.getElementById('captureCanvas');
        
        if (!video || !canvas) return;

        const context = canvas.getContext('2d');
        canvas.width = video.videoWidth;
        canvas.height = video.videoHeight;

        // رسم الإطار الحالي من الفيديو على الكانفاس
        context.drawImage(video, 0, 0, canvas.width, canvas.height);

        // تحويل الصورة إلى blob
        canvas.toBlob((blob) => {
            if (blob) {
                // إنشاء ملف من الصورة الملتقطة
                const file = new File([blob], `scan_${Date.now()}.jpg`, { type: 'image/jpeg' });
                
                // معالجة الملف كما لو تم اختياره
                window.currentAttachmentFile = file;
                
                // عرض معاينة الصورة الملتقطة
                this.showCapturedImage(canvas.toDataURL());
                
                // إيقاف الكاميرا
                this.stopScan();
                
                showNotification('تم التقاط الصورة بنجاح', 'success');
            }
        }, 'image/jpeg', 0.9);
    }

    /**
     * عرض الصورة الملتقطة
     * @param {string} dataUrl - رابط البيانات للصورة
     */
    showCapturedImage(dataUrl) {
        const previewContent = document.getElementById('previewContent');
        if (!previewContent) return;

        previewContent.innerHTML = `
            <div class="captured-image">
                <img src="${dataUrl}" class="img-fluid" style="max-height: 300px; border-radius: 8px;">
                <div class="mt-3">
                    <button class="btn btn-success me-2" onclick="scanner.acceptCapture()">
                        <i class="fas fa-check me-1"></i>قبول الصورة
                    </button>
                    <button class="btn btn-warning" onclick="scanner.retakePhoto()">
                        <i class="fas fa-redo me-1"></i>إعادة التقاط
                    </button>
                </div>
            </div>
        `;
    }

    /**
     * قبول الصورة الملتقطة
     */
    acceptCapture() {
        if (window.currentAttachmentFile) {
            showFilePreview(window.currentAttachmentFile);
            showNotification('تم قبول الصورة. يمكنك الآن حفظ المرفق', 'success');
        }
    }

    /**
     * إعادة التقاط الصورة
     */
    retakePhoto() {
        this.startCameraScan();
    }

    /**
     * بدء المسح بالماسح الضوئي
     */
    async startScannerScan() {
        if (this.scannerDevices.length === 0) {
            showNotification('لا توجد ماسحات ضوئية متاحة', 'warning');
            return;
        }

        try {
            // محاولة الاتصال بالماسح الضوئي الأول
            const scanner = this.scannerDevices[0];
            
            if (scanner.type === 'usb') {
                await this.connectUSBScanner(scanner.device);
            }

        } catch (error) {
            console.error('خطأ في المسح الضوئي:', error);
            showNotification('فشل في الاتصال بالماسح الضوئي', 'error');
        }
    }

    /**
     * الاتصال بماسح ضوئي USB
     * @param {USBDevice} device - جهاز الماسح الضوئي
     */
    async connectUSBScanner(device) {
        try {
            if (!device.opened) {
                await device.open();
            }

            // تحديد الواجهة المناسبة
            const configuration = device.configuration;
            const interface_ = configuration.interfaces[0];
            
            await device.claimInterface(interface_.interfaceNumber);

            showNotification('تم الاتصال بالماسح الضوئي بنجاح', 'success');
            
            // هنا يمكن إضافة منطق المسح الفعلي
            // هذا يتطلب معرفة بروتوكول الماسح الضوئي المحدد
            
        } catch (error) {
            console.error('خطأ في الاتصال بالماسح الضوئي:', error);
            showNotification('فشل في الاتصال بالماسح الضوئي', 'error');
        }
    }

    /**
     * إيقاف المسح
     */
    stopScan() {
        if (this.currentStream) {
            this.currentStream.getTracks().forEach(track => track.stop());
            this.currentStream = null;
        }
        
        this.isScanning = false;
        
        // إخفاء واجهة الكاميرا
        const previewArea = document.getElementById('previewArea');
        if (previewArea) {
            previewArea.style.display = 'none';
        }
    }

    /**
     * تنظيف الموارد
     */
    cleanup() {
        this.stopScan();
        
        // إغلاق اتصالات الماسحات الضوئية
        this.scannerDevices.forEach(scanner => {
            if (scanner.type === 'usb' && scanner.device.opened) {
                scanner.device.close().catch(console.error);
            }
        });
    }
}

// إنشاء مثيل من نظام المسح الضوئي
const scanner = new AdvancedScanner();

// تصدير للاستخدام العام
window.scanner = scanner;

// تنظيف الموارد عند إغلاق الصفحة
window.addEventListener('beforeunload', () => {
    scanner.cleanup();
});
