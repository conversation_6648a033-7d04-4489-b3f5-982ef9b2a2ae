{"project": {"name": "نظام أرشفة اليومية العسكرية", "version": "1.0.0", "description": "نظام شامل لإدارة وأرشفة يوميات طلاب الدورات العسكرية", "author": "AI Assistant", "license": "MIT", "created": "2024-01-20", "lastUpdated": "2024-01-20"}, "system": {"language": "ar", "direction": "rtl", "theme": "military", "offline": true, "storage": {"primary": "localStorage", "backup": "indexedDB", "maxSize": "50MB"}}, "features": {"courses": {"enabled": true, "maxCourses": 100, "validation": {"courseCodeRequired": true, "uniqueCourseCode": true, "dateValidation": true}}, "students": {"enabled": true, "maxStudentsPerCourse": 500, "validation": {"militaryNumberRequired": true, "uniqueMilitaryNumber": true, "rankValidation": true}}, "dailyReports": {"enabled": true, "maxReportsPerDay": 10, "validation": {"dateRequired": true, "supervisorRequired": true, "courseRequired": true}, "violations": {"maxPerStudent": 10, "types": ["تأخير عن الطابور", "عدم ارتداء الزي الرسمي", "عدم تنفيذ الأوامر", "سوء السلوك", "إهمال في الواجبات", "مخالفة اللوائح", "عد<PERSON> الانضباط", "أ<PERSON><PERSON><PERSON>"]}, "delays": {"maxPerDay": 7, "unit": "حصة"}, "absences": {"maxPerDay": 7, "types": ["بعذر", "بدون عذر"], "unit": "حصة"}}, "attachments": {"enabled": true, "maxFileSize": "10MB", "allowedTypes": ["application/pdf", "image/jpeg", "image/jpg", "image/png", "application/msword", "application/vnd.openxmlformats-officedocument.wordprocessingml.document"], "types": ["medical_leave", "death_certificate", "birth_certificate", "official_document", "excuse_letter", "other"], "scanner": {"enabled": true, "webcam": true, "usb": true, "network": false}}, "users": {"enabled": true, "roles": ["admin", "supervisor", "viewer"], "permissions": {"admin": ["view_all", "create_reports", "edit_reports", "delete_reports", "manage_courses", "manage_users", "export_data", "import_data"], "supervisor": ["view_assigned_courses", "create_reports", "edit_own_reports"], "viewer": ["view_assigned_courses"]}}, "notifications": {"enabled": true, "types": ["success", "warning", "error", "info"], "duration": 5000, "position": "top-end"}, "backup": {"enabled": true, "autoBackup": true, "interval": 3600000, "maxBackups": 10}}, "ui": {"colors": {"primary": "#1a365d", "secondary": "#2d3748", "accent": "#d69e2e", "success": "#38a169", "warning": "#ed8936", "danger": "#e53e3e", "light": "#f7fafc", "dark": "#1a202c"}, "fonts": {"primary": "Cairo", "fallback": ["Segoe UI", "<PERSON><PERSON><PERSON>", "Geneva", "<PERSON><PERSON><PERSON>", "sans-serif"]}, "animations": {"enabled": true, "duration": "0.3s", "easing": "ease"}, "responsive": {"enabled": true, "breakpoints": {"mobile": "768px", "tablet": "992px", "desktop": "1200px"}}}, "security": {"dataEncryption": false, "sessionTimeout": 3600000, "maxLoginAttempts": 5, "passwordPolicy": {"minLength": 8, "requireUppercase": true, "requireLowercase": true, "requireNumbers": true, "requireSpecialChars": false}}, "performance": {"lazyLoading": true, "caching": true, "compression": false, "minification": false, "maxMemoryUsage": "100MB"}, "development": {"debug": false, "logging": {"enabled": true, "level": "info", "console": true, "file": false}, "testing": {"enabled": true, "unitTests": true, "integrationTests": true}}, "deployment": {"type": "static", "platform": "local", "requirements": {"browser": "modern", "javascript": "ES6+", "storage": "localStorage"}}, "documentation": {"userManual": "README.html", "apiDocs": false, "changelog": false, "license": "MIT"}, "support": {"email": false, "phone": false, "documentation": true, "community": false}}