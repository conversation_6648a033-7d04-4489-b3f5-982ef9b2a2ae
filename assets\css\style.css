/* ===== نظام أرشفة اليومية العسكرية - ملف التصميم الرئيسي ===== */

/* ===== المتغيرات الأساسية ===== */
:root {
    --military-primary: #1a365d;      /* كحلي داكن */
    --military-secondary: #2d3748;    /* رصاصي داكن */
    --military-accent: #d69e2e;       /* ذهبي */
    --military-success: #38a169;      /* أخضر عسكري */
    --military-warning: #ed8936;      /* برتقالي */
    --military-danger: #e53e3e;       /* أحمر */
    --military-light: #f7fafc;        /* أبيض مائل للرمادي */
    --military-dark: #1a202c;         /* أسود مائل للكحلي */
    
    --gradient-primary: linear-gradient(135deg, var(--military-primary) 0%, var(--military-secondary) 100%);
    --gradient-accent: linear-gradient(135deg, var(--military-accent) 0%, #f6ad55 100%);
    
    --shadow-light: 0 2px 4px rgba(0,0,0,0.1);
    --shadow-medium: 0 4px 8px rgba(0,0,0,0.15);
    --shadow-heavy: 0 8px 16px rgba(0,0,0,0.2);
    
    --border-radius: 8px;
    --transition: all 0.3s ease;
}

/* ===== الخطوط والنصوص ===== */
* {
    font-family: 'Cairo', 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
}

body {
    background: linear-gradient(135deg, #f7fafc 0%, #edf2f7 100%);
    color: var(--military-dark);
    line-height: 1.6;
    overflow-x: hidden;
}

/* ===== شاشة التحميل ===== */
.loading-screen {
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: var(--gradient-primary);
    display: flex;
    align-items: center;
    justify-content: center;
    z-index: 9999;
    transition: opacity 0.5s ease, visibility 0.5s ease;
}

.loading-screen.hidden {
    opacity: 0;
    visibility: hidden;
}

.loading-content {
    text-align: center;
    color: white;
}

.military-logo {
    font-size: 4rem;
    color: var(--military-accent);
    margin-bottom: 1rem;
    animation: pulse 2s infinite;
}

.loading-spinner {
    width: 50px;
    height: 50px;
    border: 4px solid rgba(255,255,255,0.3);
    border-top: 4px solid var(--military-accent);
    border-radius: 50%;
    animation: spin 1s linear infinite;
    margin: 1rem auto;
}

@keyframes spin {
    0% { transform: rotate(0deg); }
    100% { transform: rotate(360deg); }
}

@keyframes pulse {
    0%, 100% { transform: scale(1); }
    50% { transform: scale(1.1); }
}

/* ===== شريط التنقل ===== */
.military-navbar {
    background: var(--gradient-primary) !important;
    box-shadow: var(--shadow-medium);
    padding: 1rem 0;
    backdrop-filter: blur(10px);
}

.navbar-brand {
    font-size: 1.5rem;
    font-weight: 700;
    color: white !important;
    text-decoration: none;
}

.navbar-brand i {
    color: var(--military-accent);
}

.nav-link {
    color: rgba(255,255,255,0.9) !important;
    font-weight: 500;
    padding: 0.5rem 1rem !important;
    border-radius: var(--border-radius);
    transition: var(--transition);
    margin: 0 0.2rem;
}

.nav-link:hover,
.nav-link.active {
    background: rgba(255,255,255,0.1);
    color: var(--military-accent) !important;
    transform: translateY(-2px);
}

.user-info {
    color: rgba(255,255,255,0.9);
    font-weight: 500;
}

.current-time {
    color: var(--military-accent);
    font-weight: 600;
    font-family: 'Courier New', monospace;
}

/* ===== المحتوى الرئيسي ===== */
.main-content {
    margin-top: 100px;
    padding: 2rem 0;
    min-height: calc(100vh - 100px);
}

.content-section {
    display: none;
    animation: fadeInUp 0.5s ease;
}

.content-section.active {
    display: block;
}

@keyframes fadeInUp {
    from {
        opacity: 0;
        transform: translateY(30px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

/* ===== بانر الترحيب ===== */
.welcome-banner {
    background: var(--gradient-primary);
    color: white;
    padding: 3rem 2rem;
    border-radius: var(--border-radius);
    margin-bottom: 2rem;
    box-shadow: var(--shadow-heavy);
    position: relative;
    overflow: hidden;
}

.welcome-banner::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: url('data:image/svg+xml,<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 100 100"><defs><pattern id="grain" width="100" height="100" patternUnits="userSpaceOnUse"><circle cx="50" cy="50" r="1" fill="rgba(255,255,255,0.05)"/></pattern></defs><rect width="100" height="100" fill="url(%23grain)"/></svg>');
    opacity: 0.3;
}

.welcome-banner .row {
    position: relative;
    z-index: 1;
}

.military-emblem {
    font-size: 6rem;
    color: var(--military-accent);
    animation: float 3s ease-in-out infinite;
}

@keyframes float {
    0%, 100% { transform: translateY(0px); }
    50% { transform: translateY(-10px); }
}

/* ===== بطاقات الإحصائيات ===== */
.stat-card {
    background: white;
    border-radius: var(--border-radius);
    padding: 2rem;
    box-shadow: var(--shadow-light);
    transition: var(--transition);
    border-left: 4px solid var(--military-primary);
    position: relative;
    overflow: hidden;
}

.stat-card::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 4px;
    background: var(--gradient-accent);
    transform: scaleX(0);
    transition: var(--transition);
}

.stat-card:hover {
    transform: translateY(-5px);
    box-shadow: var(--shadow-heavy);
}

.stat-card:hover::before {
    transform: scaleX(1);
}

.stat-card-primary { border-left-color: var(--military-primary); }
.stat-card-success { border-left-color: var(--military-success); }
.stat-card-warning { border-left-color: var(--military-warning); }
.stat-card-danger { border-left-color: var(--military-danger); }

.stat-icon {
    font-size: 2.5rem;
    margin-bottom: 1rem;
    opacity: 0.8;
}

.stat-card-primary .stat-icon { color: var(--military-primary); }
.stat-card-success .stat-icon { color: var(--military-success); }
.stat-card-warning .stat-icon { color: var(--military-warning); }
.stat-card-danger .stat-icon { color: var(--military-danger); }

.stat-content h3 {
    font-size: 2.5rem;
    font-weight: 700;
    margin-bottom: 0.5rem;
    color: var(--military-dark);
}

.stat-content p {
    color: #666;
    margin: 0;
    font-weight: 500;
}

/* ===== بطاقة الإجراءات السريعة ===== */
.quick-actions-card {
    background: white;
    border-radius: var(--border-radius);
    padding: 2rem;
    box-shadow: var(--shadow-light);
    border-top: 4px solid var(--military-accent);
}

.btn-quick-action {
    background: var(--gradient-primary);
    color: white;
    border: none;
    padding: 1.5rem;
    border-radius: var(--border-radius);
    transition: var(--transition);
    text-align: center;
    display: flex;
    flex-direction: column;
    align-items: center;
    gap: 0.5rem;
    font-weight: 600;
    box-shadow: var(--shadow-light);
}

.btn-quick-action:hover {
    background: var(--gradient-accent);
    transform: translateY(-3px);
    box-shadow: var(--shadow-medium);
    color: white;
}

.btn-quick-action i {
    font-size: 2rem;
    margin-bottom: 0.5rem;
}

/* ===== الإشعارات ===== */
.toast {
    background: white;
    border: none;
    border-radius: var(--border-radius);
    box-shadow: var(--shadow-medium);
    border-right: 4px solid var(--military-success);
}

.toast.toast-warning {
    border-right-color: var(--military-warning);
}

.toast.toast-error {
    border-right-color: var(--military-danger);
}

.toast.toast-info {
    border-right-color: var(--military-primary);
}

/* ===== التأثيرات المتحركة ===== */
.animate-slide-in {
    animation: slideIn 0.5s ease;
}

@keyframes slideIn {
    from {
        transform: translateX(100%);
        opacity: 0;
    }
    to {
        transform: translateX(0);
        opacity: 1;
    }
}

.animate-fade-in {
    animation: fadeIn 0.5s ease;
}

@keyframes fadeIn {
    from { opacity: 0; }
    to { opacity: 1; }
}

/* ===== التصميم المتجاوب ===== */
@media (max-width: 768px) {
    .main-content {
        margin-top: 80px;
        padding: 1rem 0;
    }

    .welcome-banner {
        padding: 2rem 1rem;
        text-align: center;
    }

    .welcome-banner h1 {
        font-size: 2rem;
    }

    .military-emblem {
        font-size: 4rem;
        margin-top: 1rem;
    }

    .stat-card {
        margin-bottom: 1rem;
    }

    .btn-quick-action {
        padding: 1rem;
    }

    .btn-quick-action i {
        font-size: 1.5rem;
    }
}

/* ===== تحسينات إضافية ===== */
.btn:focus,
.form-control:focus,
.form-select:focus {
    box-shadow: 0 0 0 0.2rem rgba(26, 54, 93, 0.25);
    border-color: var(--military-primary);
}

.text-military-primary { color: var(--military-primary) !important; }
.text-military-accent { color: var(--military-accent) !important; }
.bg-military-primary { background-color: var(--military-primary) !important; }
.bg-military-accent { background-color: var(--military-accent) !important; }

/* ===== تحسين الأداء ===== */
* {
    box-sizing: border-box;
}

img {
    max-width: 100%;
    height: auto;
}

.container-fluid {
    max-width: 1400px;
}

/* ===== أنماط الصفحات الفرعية ===== */
.page-header {
    background: white;
    border-radius: var(--border-radius);
    padding: 2rem;
    box-shadow: var(--shadow-light);
    border-left: 4px solid var(--military-accent);
}

.page-title {
    color: var(--military-dark);
    font-weight: 700;
    margin-bottom: 0.5rem;
}

.page-subtitle {
    color: #666;
    margin: 0;
    font-size: 1.1rem;
}

/* ===== أنماط الأزرار العسكرية ===== */
.btn-military-primary {
    background: var(--gradient-primary);
    border: none;
    color: white;
    font-weight: 600;
    padding: 0.75rem 1.5rem;
    border-radius: var(--border-radius);
    transition: var(--transition);
    box-shadow: var(--shadow-light);
}

.btn-military-primary:hover {
    background: var(--gradient-accent);
    transform: translateY(-2px);
    box-shadow: var(--shadow-medium);
    color: white;
}

.btn-military-accent {
    background: var(--gradient-accent);
    border: none;
    color: white;
    font-weight: 600;
    padding: 0.75rem 1.5rem;
    border-radius: var(--border-radius);
    transition: var(--transition);
    box-shadow: var(--shadow-light);
}

.btn-military-accent:hover {
    background: var(--gradient-primary);
    transform: translateY(-2px);
    box-shadow: var(--shadow-medium);
    color: white;
}

/* ===== أنماط البطاقات ===== */
.card {
    border: none;
    border-radius: var(--border-radius);
    box-shadow: var(--shadow-light);
    transition: var(--transition);
}

.card:hover {
    box-shadow: var(--shadow-medium);
}

.card-header {
    background: var(--gradient-primary);
    color: white;
    border-bottom: none;
    border-radius: var(--border-radius) var(--border-radius) 0 0;
    padding: 1.5rem;
}

.card-title {
    font-weight: 600;
    margin: 0;
}

/* ===== أنماط الجداول ===== */
.table {
    margin: 0;
}

.table th {
    background: var(--military-dark);
    color: white;
    font-weight: 600;
    border: none;
    padding: 1rem;
}

.table td {
    padding: 1rem;
    vertical-align: middle;
    border-color: #e9ecef;
}

.table-hover tbody tr:hover {
    background-color: rgba(26, 54, 93, 0.05);
}

/* ===== أنماط النماذج ===== */
.form-label {
    font-weight: 600;
    color: var(--military-dark);
    margin-bottom: 0.5rem;
}

.form-control,
.form-select {
    border: 2px solid #e9ecef;
    border-radius: var(--border-radius);
    padding: 0.75rem;
    transition: var(--transition);
}

.form-control:focus,
.form-select:focus {
    border-color: var(--military-primary);
    box-shadow: 0 0 0 0.2rem rgba(26, 54, 93, 0.25);
}

.form-text {
    color: #6c757d;
    font-size: 0.875rem;
}

/* ===== أنماط النوافذ المنبثقة ===== */
.modal-content {
    border: none;
    border-radius: var(--border-radius);
    box-shadow: var(--shadow-heavy);
}

.modal-header {
    border-bottom: none;
    padding: 1.5rem;
}

.modal-body {
    padding: 2rem;
}

.modal-footer {
    border-top: none;
    padding: 1.5rem;
}

/* ===== أنماط الشارات ===== */
.badge {
    font-size: 0.875rem;
    padding: 0.5rem 0.75rem;
    border-radius: var(--border-radius);
}

/* ===== أنماط مجموعات الأزرار ===== */
.btn-group-sm .btn {
    padding: 0.375rem 0.5rem;
    font-size: 0.875rem;
}

/* ===== أنماط بطاقات الطلاب ===== */
.student-card {
    transition: var(--transition);
}

.student-card:hover {
    transform: translateY(-2px);
}

.student-card .card {
    border: 2px solid #e9ecef;
    transition: var(--transition);
}

.student-card:hover .card {
    border-color: var(--military-primary);
    box-shadow: var(--shadow-medium);
}

.violations-list {
    max-height: 120px;
    overflow-y: auto;
}

.violation-item {
    background: #fff3cd;
    border: 1px solid #ffeaa7;
    border-radius: 4px;
    padding: 0.5rem;
    margin-bottom: 0.5rem;
    font-size: 0.875rem;
    position: relative;
}

.violation-item .remove-violation {
    position: absolute;
    top: 2px;
    left: 2px;
    background: #dc3545;
    color: white;
    border: none;
    border-radius: 50%;
    width: 20px;
    height: 20px;
    font-size: 0.75rem;
    line-height: 1;
    cursor: pointer;
}

.form-label-sm {
    font-size: 0.875rem;
    margin-bottom: 0.25rem;
}

/* ===== أنماط المرفقات ===== */
.attachment-item {
    background: white;
    border: 2px dashed #dee2e6;
    border-radius: var(--border-radius);
    padding: 1rem;
    margin-bottom: 1rem;
    text-align: center;
    transition: var(--transition);
}

.attachment-item:hover {
    border-color: var(--military-primary);
    background: #f8f9fa;
}

.attachment-preview {
    max-width: 100px;
    max-height: 100px;
    object-fit: cover;
    border-radius: 4px;
    margin-bottom: 0.5rem;
}

.attachment-info {
    font-size: 0.875rem;
    color: #666;
}

.attachment-actions {
    margin-top: 0.5rem;
}

.attachment-actions .btn {
    margin: 0 0.25rem;
}

/* ===== أنماط منطقة السحب والإفلات ===== */
.drop-zone {
    border: 2px dashed #dee2e6;
    border-radius: var(--border-radius);
    padding: 2rem;
    text-align: center;
    background: #f8f9fa;
    transition: var(--transition);
    cursor: pointer;
}

.drop-zone:hover,
.drop-zone.dragover {
    border-color: var(--military-primary);
    background: rgba(26, 54, 93, 0.05);
}

.drop-zone i {
    font-size: 3rem;
    color: #dee2e6;
    margin-bottom: 1rem;
}

.drop-zone:hover i,
.drop-zone.dragover i {
    color: var(--military-primary);
}

/* ===== أنماط إضافية للنماذج ===== */
.form-control-sm {
    font-size: 0.875rem;
    padding: 0.375rem 0.5rem;
}

.form-select-sm {
    font-size: 0.875rem;
    padding: 0.375rem 0.5rem;
}

/* ===== أنماط الإشعارات المخصصة ===== */
.alert-military {
    background: linear-gradient(135deg, rgba(26, 54, 93, 0.1) 0%, rgba(45, 55, 72, 0.1) 100%);
    border: 1px solid var(--military-primary);
    border-radius: var(--border-radius);
    color: var(--military-dark);
}

.alert-military .alert-heading {
    color: var(--military-primary);
    font-weight: 700;
}

/* ===== تحسينات للطباعة ===== */
@media print {
    .navbar,
    .btn,
    .modal,
    .toast-container {
        display: none !important;
    }

    .main-content {
        margin-top: 0;
    }

    .card {
        border: 1px solid #000;
        box-shadow: none;
    }

    .page-header {
        border-bottom: 2px solid #000;
        margin-bottom: 1rem;
    }
}

/* ===== التأثيرات المتحركة ===== */
.animate-slide-in {
    animation: slideIn 0.5s ease;
}

@keyframes slideIn {
    from {
        transform: translateX(100%);
        opacity: 0;
    }
    to {
        transform: translateX(0);
        opacity: 1;
    }
}

.animate-fade-in {
    animation: fadeIn 0.5s ease;
}

@keyframes fadeIn {
    from { opacity: 0; }
    to { opacity: 1; }
}

/* ===== التصميم المتجاوب ===== */
@media (max-width: 768px) {
    .main-content {
        margin-top: 80px;
        padding: 1rem 0;
    }
    
    .welcome-banner {
        padding: 2rem 1rem;
        text-align: center;
    }
    
    .welcome-banner h1 {
        font-size: 2rem;
    }
    
    .military-emblem {
        font-size: 4rem;
        margin-top: 1rem;
    }
    
    .stat-card {
        margin-bottom: 1rem;
    }
    
    .btn-quick-action {
        padding: 1rem;
    }
    
    .btn-quick-action i {
        font-size: 1.5rem;
    }
}

/* ===== تحسينات إضافية ===== */
.btn:focus,
.form-control:focus,
.form-select:focus {
    box-shadow: 0 0 0 0.2rem rgba(26, 54, 93, 0.25);
    border-color: var(--military-primary);
}

.text-military-primary { color: var(--military-primary) !important; }
.text-military-accent { color: var(--military-accent) !important; }
.bg-military-primary { background-color: var(--military-primary) !important; }
.bg-military-accent { background-color: var(--military-accent) !important; }

/* ===== تحسين الأداء ===== */
* {
    box-sizing: border-box;
}

img {
    max-width: 100%;
    height: auto;
}

.container-fluid {
    max-width: 1400px;
}
