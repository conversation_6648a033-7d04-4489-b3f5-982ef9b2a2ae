/* ===== نظام أرشفة اليومية العسكرية - ملف التصميم الرئيسي ===== */

/* ===== المتغيرات الأساسية ===== */
:root {
    --military-primary: #1a365d;      /* كحلي داكن */
    --military-secondary: #2d3748;    /* رصاصي داكن */
    --military-accent: #d69e2e;       /* ذهبي */
    --military-success: #38a169;      /* أخضر عسكري */
    --military-warning: #ed8936;      /* برتقالي */
    --military-danger: #e53e3e;       /* أحمر */
    --military-light: #f7fafc;        /* أبيض مائل للرمادي */
    --military-dark: #1a202c;         /* أسود مائل للكحلي */
    
    --gradient-primary: linear-gradient(135deg, var(--military-primary) 0%, var(--military-secondary) 100%);
    --gradient-accent: linear-gradient(135deg, var(--military-accent) 0%, #f6ad55 100%);
    
    --shadow-light: 0 2px 4px rgba(0,0,0,0.1);
    --shadow-medium: 0 4px 8px rgba(0,0,0,0.15);
    --shadow-heavy: 0 8px 16px rgba(0,0,0,0.2);
    
    --border-radius: 8px;
    --transition: all 0.3s ease;
}

/* ===== الخطوط والنصوص ===== */
* {
    font-family: 'Cairo', 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
}

body {
    background: linear-gradient(135deg, #f7fafc 0%, #edf2f7 100%);
    color: var(--military-dark);
    line-height: 1.6;
    overflow-x: hidden;
}

/* ===== شاشة التحميل ===== */
.loading-screen {
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: var(--gradient-primary);
    display: flex;
    align-items: center;
    justify-content: center;
    z-index: 9999;
    transition: opacity 0.5s ease, visibility 0.5s ease;
}

.loading-screen.hidden {
    opacity: 0;
    visibility: hidden;
}

.loading-content {
    text-align: center;
    color: white;
}

.military-logo {
    font-size: 4rem;
    color: var(--military-accent);
    margin-bottom: 1rem;
    animation: pulse 2s infinite;
}

.loading-spinner {
    width: 50px;
    height: 50px;
    border: 4px solid rgba(255,255,255,0.3);
    border-top: 4px solid var(--military-accent);
    border-radius: 50%;
    animation: spin 1s linear infinite;
    margin: 1rem auto;
}

@keyframes spin {
    0% { transform: rotate(0deg); }
    100% { transform: rotate(360deg); }
}

@keyframes pulse {
    0%, 100% { transform: scale(1); }
    50% { transform: scale(1.1); }
}

/* ===== شريط التنقل ===== */
.military-navbar {
    background: var(--gradient-primary) !important;
    box-shadow: var(--shadow-medium);
    padding: 1rem 0;
    backdrop-filter: blur(10px);
}

.navbar-brand {
    font-size: 1.5rem;
    font-weight: 700;
    color: white !important;
    text-decoration: none;
}

.navbar-brand i {
    color: var(--military-accent);
}

.nav-link {
    color: rgba(255,255,255,0.9) !important;
    font-weight: 500;
    padding: 0.5rem 1rem !important;
    border-radius: var(--border-radius);
    transition: var(--transition);
    margin: 0 0.2rem;
}

.nav-link:hover,
.nav-link.active {
    background: rgba(255,255,255,0.1);
    color: var(--military-accent) !important;
    transform: translateY(-2px);
}

.user-info {
    color: rgba(255,255,255,0.9);
    font-weight: 500;
}

.current-time {
    color: var(--military-accent);
    font-weight: 600;
    font-family: 'Courier New', monospace;
}

/* ===== المحتوى الرئيسي ===== */
.main-content {
    margin-top: 100px;
    padding: 2rem 0;
    min-height: calc(100vh - 100px);
}

.content-section {
    display: none;
    animation: fadeInUp 0.5s ease;
}

.content-section.active {
    display: block;
}

@keyframes fadeInUp {
    from {
        opacity: 0;
        transform: translateY(30px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

/* ===== بانر الترحيب ===== */
.welcome-banner {
    background: var(--gradient-primary);
    color: white;
    padding: 3rem 2rem;
    border-radius: var(--border-radius);
    margin-bottom: 2rem;
    box-shadow: var(--shadow-heavy);
    position: relative;
    overflow: hidden;
}

.welcome-banner::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: url('data:image/svg+xml,<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 100 100"><defs><pattern id="grain" width="100" height="100" patternUnits="userSpaceOnUse"><circle cx="50" cy="50" r="1" fill="rgba(255,255,255,0.05)"/></pattern></defs><rect width="100" height="100" fill="url(%23grain)"/></svg>');
    opacity: 0.3;
}

.welcome-banner .row {
    position: relative;
    z-index: 1;
}

.military-emblem {
    font-size: 6rem;
    color: var(--military-accent);
    animation: float 3s ease-in-out infinite;
}

@keyframes float {
    0%, 100% { transform: translateY(0px); }
    50% { transform: translateY(-10px); }
}

/* ===== بطاقات الإحصائيات ===== */
.stat-card {
    background: white;
    border-radius: var(--border-radius);
    padding: 2rem;
    box-shadow: var(--shadow-light);
    transition: var(--transition);
    border-left: 4px solid var(--military-primary);
    position: relative;
    overflow: hidden;
}

.stat-card::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 4px;
    background: var(--gradient-accent);
    transform: scaleX(0);
    transition: var(--transition);
}

.stat-card:hover {
    transform: translateY(-5px);
    box-shadow: var(--shadow-heavy);
}

.stat-card:hover::before {
    transform: scaleX(1);
}

.stat-card-primary { border-left-color: var(--military-primary); }
.stat-card-success { border-left-color: var(--military-success); }
.stat-card-warning { border-left-color: var(--military-warning); }
.stat-card-danger { border-left-color: var(--military-danger); }

.stat-icon {
    font-size: 2.5rem;
    margin-bottom: 1rem;
    opacity: 0.8;
}

.stat-card-primary .stat-icon { color: var(--military-primary); }
.stat-card-success .stat-icon { color: var(--military-success); }
.stat-card-warning .stat-icon { color: var(--military-warning); }
.stat-card-danger .stat-icon { color: var(--military-danger); }

.stat-content h3 {
    font-size: 2.5rem;
    font-weight: 700;
    margin-bottom: 0.5rem;
    color: var(--military-dark);
}

.stat-content p {
    color: #666;
    margin: 0;
    font-weight: 500;
}

/* ===== بطاقة الإجراءات السريعة ===== */
.quick-actions-card {
    background: white;
    border-radius: var(--border-radius);
    padding: 2rem;
    box-shadow: var(--shadow-light);
    border-top: 4px solid var(--military-accent);
}

.btn-quick-action {
    background: var(--gradient-primary);
    color: white;
    border: none;
    padding: 1.5rem;
    border-radius: var(--border-radius);
    transition: var(--transition);
    text-align: center;
    display: flex;
    flex-direction: column;
    align-items: center;
    gap: 0.5rem;
    font-weight: 600;
    box-shadow: var(--shadow-light);
}

.btn-quick-action:hover {
    background: var(--gradient-accent);
    transform: translateY(-3px);
    box-shadow: var(--shadow-medium);
    color: white;
}

.btn-quick-action i {
    font-size: 2rem;
    margin-bottom: 0.5rem;
}

/* ===== الإشعارات ===== */
.toast {
    background: white;
    border: none;
    border-radius: var(--border-radius);
    box-shadow: var(--shadow-medium);
    border-right: 4px solid var(--military-success);
}

.toast.toast-warning {
    border-right-color: var(--military-warning);
}

.toast.toast-error {
    border-right-color: var(--military-danger);
}

.toast.toast-info {
    border-right-color: var(--military-primary);
}

/* ===== التأثيرات المتحركة ===== */
.animate-slide-in {
    animation: slideIn 0.5s ease;
}

@keyframes slideIn {
    from {
        transform: translateX(100%);
        opacity: 0;
    }
    to {
        transform: translateX(0);
        opacity: 1;
    }
}

.animate-fade-in {
    animation: fadeIn 0.5s ease;
}

@keyframes fadeIn {
    from { opacity: 0; }
    to { opacity: 1; }
}

/* ===== التصميم المتجاوب ===== */
@media (max-width: 768px) {
    .main-content {
        margin-top: 80px;
        padding: 1rem 0;
    }
    
    .welcome-banner {
        padding: 2rem 1rem;
        text-align: center;
    }
    
    .welcome-banner h1 {
        font-size: 2rem;
    }
    
    .military-emblem {
        font-size: 4rem;
        margin-top: 1rem;
    }
    
    .stat-card {
        margin-bottom: 1rem;
    }
    
    .btn-quick-action {
        padding: 1rem;
    }
    
    .btn-quick-action i {
        font-size: 1.5rem;
    }
}

/* ===== تحسينات إضافية ===== */
.btn:focus,
.form-control:focus,
.form-select:focus {
    box-shadow: 0 0 0 0.2rem rgba(26, 54, 93, 0.25);
    border-color: var(--military-primary);
}

.text-military-primary { color: var(--military-primary) !important; }
.text-military-accent { color: var(--military-accent) !important; }
.bg-military-primary { background-color: var(--military-primary) !important; }
.bg-military-accent { background-color: var(--military-accent) !important; }

/* ===== تحسين الأداء ===== */
* {
    box-sizing: border-box;
}

img {
    max-width: 100%;
    height: auto;
}

.container-fluid {
    max-width: 1400px;
}
