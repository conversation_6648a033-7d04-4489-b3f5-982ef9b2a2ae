# سجل التغييرات - نظام أرشفة اليومية العسكرية

جميع التغييرات المهمة في هذا المشروع سيتم توثيقها في هذا الملف.

التنسيق مبني على [Keep a Changelog](https://keepachangelog.com/ar/1.0.0/)،
وهذا المشروع يتبع [Semantic Versioning](https://semver.org/lang/ar/).

## [غير منشور]

### مخطط له
- إضافة نظام التقارير المتقدمة
- دعم التصدير إلى Excel
- إضافة نظام الإشعارات المتقدم
- دعم المزامنة السحابية
- إضافة نظام الصلاحيات المتقدم

## [1.0.0] - 2024-01-20

### مضاف
- **الواجهة الرئيسية**
  - تصميم عسكري فخم مع دعم كامل للغة العربية (RTL)
  - شريط تنقل متجاوب مع أيقونات عالية الجودة
  - لوحة تحكم رئيسية مع إحصائيات تفاعلية
  - تأثيرات حركة احترافية ومناسبة للبيئة العسكرية

- **نظام إدارة الدورات**
  - إضافة وتعديل وحذف الدورات العسكرية
  - حقول شاملة: رمز الدورة، رقم الدورة، اسم الدورة، تواريخ البداية والنهاية
  - التحقق من صحة البيانات وعدم التكرار
  - إحصائيات مفصلة للدورات (نشطة، مكتملة، قادمة)
  - جدول تفاعلي مع إمكانيات البحث والفلترة

- **نظام إدارة الطلاب**
  - إضافة طلاب جدد مع ربطهم بالدورات
  - حقول شاملة: الاسم الكامل، الرقم العسكري، الرتبة، بيانات الدورة
  - البحث التلقائي في قاعدة البيانات عند إدخال الرقم العسكري
  - استيراد تلقائي لبيانات الدورة عند اختيار رمز ورقم الدورة
  - التحقق من صحة البيانات وعدم تكرار الأرقام العسكرية

- **نظام اليوميات**
  - نموذج شامل لتسجيل اليومية اليومية
  - إدخال تلقائي للتاريخ مع إمكانية التعديل
  - ربط اليومية بدورة محددة مع تحميل قائمة الطلاب تلقائياً
  - تسجيل المخالفات مع قائمة منسدلة للأنواع المختلفة
  - تسجيل التأخيرات (حتى 7 حصص)
  - تسجيل الغيابات (بعذر/بدون عذر، حتى 7 حصص)
  - بطاقات تفاعلية لكل طالب مع جميع البيانات المطلوبة

- **نظام المرفقات والمسح الضوئي**
  - رفع الملفات بالسحب والإفلات
  - دعم أنواع ملفات متعددة: PDF, JPG, PNG, DOC, DOCX
  - التحقق من حجم الملفات (حد أقصى 10 ميجابايت)
  - معاينة فورية للملفات المرفوعة
  - نظام مسح ضوئي متقدم:
    - المسح بالكاميرا مع واجهة التقاط احترافية
    - دعم الماسحات الضوئية المتصلة عبر USB
    - اكتشاف تلقائي للماسحات المتاحة
  - تصنيف المرفقات حسب النوع (إجازة مرضية، شهادة وفاة، إلخ)
  - ربط المرفقات بطلاب محددين

- **نظام التخزين المحلي**
  - حفظ جميع البيانات محلياً باستخدام LocalStorage
  - دعم احتياطي باستخدام IndexedDB
  - تصدير واستيراد البيانات بصيغة JSON
  - نظام نسخ احتياطي تلقائي

- **نظام الإشعارات**
  - إشعارات فورية (Toast Notifications) في الركن العلوي
  - أنواع مختلفة: نجاح، تحذير، خطأ، معلومات
  - تأثيرات بصرية جذابة مع أيقونات مناسبة
  - إشعارات تلقائية عند إضافة المخالفات أو المرفقات

- **الإحصائيات والتقارير**
  - إحصائيات شاملة في لوحة التحكم
  - عدد الدورات، الطلاب، اليوميات، المخالفات
  - إحصائيات مفصلة لكل قسم
  - تحديث تلقائي للإحصائيات

- **التصميم والواجهة**
  - ألوان عسكرية احترافية (كحلي، رصاصي، ذهبي)
  - خطوط عربية عالية الجودة (Cairo)
  - تصميم متجاوب يعمل على جميع الأجهزة
  - تأثيرات حركة بسيطة وأنيقة
  - أيقونات Font Awesome عالية الجودة

- **الملفات والوثائق**
  - دليل مستخدم HTML فخم ومفصل
  - صفحة اختبار شاملة لجميع الوظائف
  - ملف تكوين JSON مفصل
  - ملف package.json للمشروع
  - بيانات تجريبية للاختبار

### تقني
- **التقنيات المستخدمة**
  - HTML5 مع دعم كامل للعربية (RTL)
  - CSS3 مع متغيرات مخصصة وتأثيرات متقدمة
  - JavaScript ES6+ مع برمجة كائنية التوجه
  - Bootstrap 5 RTL للتصميم المتجاوب
  - Font Awesome 6 للأيقونات
  - Web APIs للمسح الضوئي والكاميرا

- **الأداء والتحسين**
  - تحميل سريع مع تحسين الصور والملفات
  - ذاكرة تخزين محلية فعالة
  - كود منظم ومعلق باللغة العربية
  - معالجة أخطاء شاملة
  - تصميم متجاوب ومحسن للأداء

- **الأمان والموثوقية**
  - التحقق من صحة البيانات في الواجهة والخلفية
  - معالجة آمنة للملفات المرفوعة
  - حماية من الأخطاء الشائعة
  - نسخ احتياطي تلقائي للبيانات

### الملفات المضافة
```
├── index.html              # الصفحة الرئيسية
├── README.html             # دليل المستخدم
├── test.html              # صفحة الاختبار
├── config.json            # ملف التكوين
├── package.json           # معلومات المشروع
├── CHANGELOG.md           # سجل التغييرات
├── assets/
│   ├── css/
│   │   └── style.css      # ملف التصميم الرئيسي
│   └── js/
│       ├── app.js         # ملف JavaScript الرئيسي
│       └── scanner.js     # نظام المسح الضوئي
└── data/
    └── data.json          # البيانات التجريبية
```

### متطلبات النظام
- متصفح ويب حديث (Chrome, Firefox, Edge, Safari)
- دعم JavaScript ES6+
- دعم LocalStorage و IndexedDB
- دعم Web APIs للكاميرا والماسحات الضوئية
- مساحة تخزين: 50 ميجابايت على الأقل

### طريقة التشغيل
1. تحميل جميع الملفات في مجلد واحد
2. فتح ملف `index.html` في المتصفح
3. البدء بإضافة الدورات والطلاب
4. تسجيل اليوميات اليومية

---

## معلومات الإصدار

**الإصدار:** 1.0.0  
**تاريخ الإصدار:** 20 يناير 2024  
**المطور:** AI Assistant  
**الترخيص:** MIT  
**اللغة:** العربية (RTL)  
**المنصة:** Web (Offline)
