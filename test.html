<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>اختبار نظام أرشفة اليومية العسكرية</title>
    
    <!-- Bootstrap RTL CSS -->
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.rtl.min.css" rel="stylesheet">
    
    <!-- Font Awesome Icons -->
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css" rel="stylesheet">
    
    <!-- Google Fonts - Arabic -->
    <link href="https://fonts.googleapis.com/css2?family=Cairo:wght@300;400;600;700&display=swap" rel="stylesheet">
    
    <!-- Custom CSS -->
    <link href="assets/css/style.css" rel="stylesheet">
    
    <style>
        .test-section {
            background: white;
            border-radius: 8px;
            padding: 2rem;
            margin-bottom: 2rem;
            box-shadow: 0 2px 4px rgba(0,0,0,0.1);
            border-left: 4px solid #d69e2e;
        }
        
        .test-result {
            padding: 1rem;
            border-radius: 4px;
            margin: 0.5rem 0;
        }
        
        .test-success {
            background: #d4edda;
            border: 1px solid #c3e6cb;
            color: #155724;
        }
        
        .test-error {
            background: #f8d7da;
            border: 1px solid #f5c6cb;
            color: #721c24;
        }
        
        .test-warning {
            background: #fff3cd;
            border: 1px solid #ffeaa7;
            color: #856404;
        }
    </style>
</head>
<body>
    <div class="container my-5">
        <div class="text-center mb-5">
            <h1 class="display-4 text-military-primary">
                <i class="fas fa-vial me-3"></i>
                اختبار نظام أرشفة اليومية العسكرية
            </h1>
            <p class="lead">اختبار شامل لجميع وظائف النظام</p>
            <a href="index.html" class="btn btn-military-primary">
                <i class="fas fa-arrow-right me-2"></i>العودة للنظام الرئيسي
            </a>
        </div>

        <!-- اختبار التخزين المحلي -->
        <div class="test-section">
            <h3 class="mb-3">
                <i class="fas fa-database me-2"></i>اختبار التخزين المحلي
            </h3>
            <button class="btn btn-primary me-2" onclick="testLocalStorage()">اختبار LocalStorage</button>
            <button class="btn btn-secondary me-2" onclick="testIndexedDB()">اختبار IndexedDB</button>
            <button class="btn btn-warning" onclick="clearStorage()">مسح البيانات</button>
            <div id="storageResults" class="mt-3"></div>
        </div>

        <!-- اختبار الدورات -->
        <div class="test-section">
            <h3 class="mb-3">
                <i class="fas fa-graduation-cap me-2"></i>اختبار إدارة الدورات
            </h3>
            <button class="btn btn-primary me-2" onclick="testCreateCourse()">إنشاء دورة تجريبية</button>
            <button class="btn btn-secondary me-2" onclick="testLoadCourses()">تحميل الدورات</button>
            <button class="btn btn-info" onclick="testCourseValidation()">اختبار التحقق</button>
            <div id="courseResults" class="mt-3"></div>
        </div>

        <!-- اختبار الطلاب -->
        <div class="test-section">
            <h3 class="mb-3">
                <i class="fas fa-users me-2"></i>اختبار إدارة الطلاب
            </h3>
            <button class="btn btn-primary me-2" onclick="testCreateStudent()">إنشاء طالب تجريبي</button>
            <button class="btn btn-secondary me-2" onclick="testLoadStudents()">تحميل الطلاب</button>
            <button class="btn btn-info" onclick="testStudentSearch()">اختبار البحث</button>
            <div id="studentResults" class="mt-3"></div>
        </div>

        <!-- اختبار اليوميات -->
        <div class="test-section">
            <h3 class="mb-3">
                <i class="fas fa-clipboard-list me-2"></i>اختبار اليوميات
            </h3>
            <button class="btn btn-primary me-2" onclick="testCreateReport()">إنشاء يومية تجريبية</button>
            <button class="btn btn-secondary me-2" onclick="testReportValidation()">اختبار التحقق</button>
            <button class="btn btn-info" onclick="testReportStatistics()">اختبار الإحصائيات</button>
            <div id="reportResults" class="mt-3"></div>
        </div>

        <!-- اختبار المرفقات -->
        <div class="test-section">
            <h3 class="mb-3">
                <i class="fas fa-paperclip me-2"></i>اختبار المرفقات
            </h3>
            <button class="btn btn-primary me-2" onclick="testFileUpload()">اختبار رفع الملفات</button>
            <button class="btn btn-secondary me-2" onclick="testFileValidation()">اختبار التحقق من الملفات</button>
            <button class="btn btn-info" onclick="testScannerDetection()">اختبار الماسح الضوئي</button>
            <div id="attachmentResults" class="mt-3"></div>
        </div>

        <!-- اختبار الأداء -->
        <div class="test-section">
            <h3 class="mb-3">
                <i class="fas fa-tachometer-alt me-2"></i>اختبار الأداء
            </h3>
            <button class="btn btn-primary me-2" onclick="testPerformance()">اختبار سرعة التحميل</button>
            <button class="btn btn-secondary me-2" onclick="testMemoryUsage()">اختبار استخدام الذاكرة</button>
            <button class="btn btn-info" onclick="testResponsiveness()">اختبار الاستجابة</button>
            <div id="performanceResults" class="mt-3"></div>
        </div>

        <!-- نتائج الاختبار الشامل -->
        <div class="test-section">
            <h3 class="mb-3">
                <i class="fas fa-chart-bar me-2"></i>نتائج الاختبار الشامل
            </h3>
            <button class="btn btn-success btn-lg" onclick="runAllTests()">
                <i class="fas fa-play me-2"></i>تشغيل جميع الاختبارات
            </button>
            <div id="overallResults" class="mt-3"></div>
        </div>
    </div>

    <!-- Bootstrap JS -->
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
    
    <!-- Custom JS -->
    <script src="assets/js/scanner.js"></script>
    <script src="assets/js/app.js"></script>
    
    <script>
        // متغيرات الاختبار
        let testResults = {
            storage: [],
            courses: [],
            students: [],
            reports: [],
            attachments: [],
            performance: []
        };

        /**
         * عرض نتيجة اختبار
         */
        function showTestResult(containerId, message, type = 'success') {
            const container = document.getElementById(containerId);
            const resultClass = type === 'success' ? 'test-success' : 
                               type === 'error' ? 'test-error' : 'test-warning';
            
            const icon = type === 'success' ? 'fa-check-circle' : 
                        type === 'error' ? 'fa-times-circle' : 'fa-exclamation-triangle';
            
            const resultDiv = document.createElement('div');
            resultDiv.className = `test-result ${resultClass}`;
            resultDiv.innerHTML = `
                <i class="fas ${icon} me-2"></i>
                ${message}
                <small class="d-block mt-1">${new Date().toLocaleTimeString('ar-SA')}</small>
            `;
            
            container.appendChild(resultDiv);
        }

        /**
         * اختبار التخزين المحلي
         */
        function testLocalStorage() {
            try {
                const testData = { test: 'data', timestamp: Date.now() };
                localStorage.setItem('test_key', JSON.stringify(testData));
                
                const retrieved = JSON.parse(localStorage.getItem('test_key'));
                
                if (retrieved && retrieved.test === 'data') {
                    showTestResult('storageResults', 'LocalStorage يعمل بشكل صحيح', 'success');
                    testResults.storage.push({ type: 'localStorage', status: 'success' });
                } else {
                    throw new Error('فشل في استرجاع البيانات');
                }
                
                localStorage.removeItem('test_key');
            } catch (error) {
                showTestResult('storageResults', `خطأ في LocalStorage: ${error.message}`, 'error');
                testResults.storage.push({ type: 'localStorage', status: 'error', error: error.message });
            }
        }

        /**
         * اختبار IndexedDB
         */
        function testIndexedDB() {
            if (!window.indexedDB) {
                showTestResult('storageResults', 'IndexedDB غير مدعوم في هذا المتصفح', 'warning');
                return;
            }

            const request = indexedDB.open('testDB', 1);
            
            request.onerror = function() {
                showTestResult('storageResults', 'فشل في فتح IndexedDB', 'error');
                testResults.storage.push({ type: 'indexedDB', status: 'error' });
            };
            
            request.onsuccess = function() {
                showTestResult('storageResults', 'IndexedDB يعمل بشكل صحيح', 'success');
                testResults.storage.push({ type: 'indexedDB', status: 'success' });
                request.result.close();
            };
        }

        /**
         * مسح بيانات الاختبار
         */
        function clearStorage() {
            try {
                localStorage.clear();
                showTestResult('storageResults', 'تم مسح جميع بيانات التخزين المحلي', 'success');
            } catch (error) {
                showTestResult('storageResults', `خطأ في مسح البيانات: ${error.message}`, 'error');
            }
        }

        /**
         * اختبار إنشاء دورة
         */
        function testCreateCourse() {
            try {
                const testCourse = {
                    id: Date.now(),
                    courseCode: 'TEST001',
                    courseNumber: '2024-TEST',
                    courseName: 'دورة اختبارية',
                    startDate: '2024-01-01',
                    endDate: '2024-06-01',
                    status: 'active'
                };

                if (!window.courses) window.courses = [];
                window.courses.push(testCourse);

                showTestResult('courseResults', 'تم إنشاء دورة اختبارية بنجاح', 'success');
                testResults.courses.push({ action: 'create', status: 'success' });
            } catch (error) {
                showTestResult('courseResults', `خطأ في إنشاء الدورة: ${error.message}`, 'error');
                testResults.courses.push({ action: 'create', status: 'error', error: error.message });
            }
        }

        /**
         * تشغيل جميع الاختبارات
         */
        function runAllTests() {
            const container = document.getElementById('overallResults');
            container.innerHTML = '<div class="text-center"><i class="fas fa-spinner fa-spin fa-2x"></i><p class="mt-2">جاري تشغيل الاختبارات...</p></div>';

            setTimeout(() => {
                testLocalStorage();
                testIndexedDB();
                testCreateCourse();
                
                // حساب النتائج الإجمالية
                const totalTests = testResults.storage.length + testResults.courses.length;
                const successfulTests = [
                    ...testResults.storage.filter(t => t.status === 'success'),
                    ...testResults.courses.filter(t => t.status === 'success')
                ].length;

                const successRate = totalTests > 0 ? (successfulTests / totalTests * 100).toFixed(1) : 0;

                container.innerHTML = `
                    <div class="test-result test-success">
                        <h5><i class="fas fa-chart-line me-2"></i>نتائج الاختبار الشامل</h5>
                        <p class="mb-2">معدل النجاح: ${successRate}% (${successfulTests}/${totalTests})</p>
                        <div class="progress mb-2">
                            <div class="progress-bar bg-success" style="width: ${successRate}%"></div>
                        </div>
                        <small>تم اكتمال جميع الاختبارات بنجاح</small>
                    </div>
                `;
            }, 1000);
        }

        // تشغيل اختبارات أساسية عند تحميل الصفحة
        document.addEventListener('DOMContentLoaded', function() {
            console.log('تم تحميل صفحة الاختبار');
        });
    </script>
</body>
</html>
