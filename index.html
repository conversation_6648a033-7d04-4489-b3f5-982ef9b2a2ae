<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>نظام أرشفة اليومية العسكرية</title>
    
    <!-- Bootstrap RTL CSS -->
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.rtl.min.css" rel="stylesheet">
    
    <!-- Font Awesome Icons -->
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css" rel="stylesheet">
    
    <!-- Google Fonts - Arabic -->
    <link href="https://fonts.googleapis.com/css2?family=Cairo:wght@300;400;600;700&display=swap" rel="stylesheet">
    
    <!-- Custom CSS -->
    <link href="assets/css/style.css" rel="stylesheet">
</head>
<body>
    <!-- Loading Screen -->
    <div id="loadingScreen" class="loading-screen">
        <div class="loading-content">
            <div class="military-logo">
                <i class="fas fa-shield-alt"></i>
            </div>
            <h2>نظام أرشفة اليومية العسكرية</h2>
            <div class="loading-spinner"></div>
            <p>جاري تحميل النظام...</p>
        </div>
    </div>

    <!-- Navigation Bar -->
    <nav class="navbar navbar-expand-lg navbar-dark military-navbar fixed-top">
        <div class="container-fluid">
            <a class="navbar-brand" href="#">
                <i class="fas fa-shield-alt me-2"></i>
                نظام أرشفة اليومية العسكرية
            </a>
            
            <button class="navbar-toggler" type="button" data-bs-toggle="collapse" data-bs-target="#navbarNav">
                <span class="navbar-toggler-icon"></span>
            </button>
            
            <div class="collapse navbar-collapse" id="navbarNav">
                <ul class="navbar-nav me-auto">
                    <li class="nav-item">
                        <a class="nav-link active" href="#" onclick="showSection('dashboard')">
                            <i class="fas fa-home me-1"></i>الرئيسية
                        </a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link" href="#" onclick="showSection('courses')">
                            <i class="fas fa-graduation-cap me-1"></i>إدارة الدورات
                        </a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link" href="#" onclick="showSection('daily-report')">
                            <i class="fas fa-clipboard-list me-1"></i>إضافة اليومية
                        </a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link" href="#" onclick="showSection('reports-history')">
                            <i class="fas fa-history me-1"></i>سجل اليوميات
                        </a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link" href="#" onclick="showSection('users')">
                            <i class="fas fa-users me-1"></i>المستخدمين
                        </a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link" href="#" onclick="showSection('settings')">
                            <i class="fas fa-cog me-1"></i>الإعدادات
                        </a>
                    </li>
                </ul>
                
                <div class="navbar-text">
                    <span class="user-info">
                        <i class="fas fa-user-circle me-1"></i>
                        <span id="currentUser">المشرف الرئيسي</span>
                    </span>
                    <span class="current-time ms-3" id="currentTime"></span>
                </div>
            </div>
        </div>
    </nav>

    <!-- Main Content -->
    <main class="main-content">
        <!-- Dashboard Section -->
        <section id="dashboard" class="content-section active">
            <div class="container-fluid">
                <div class="welcome-banner">
                    <div class="row align-items-center">
                        <div class="col-md-8">
                            <h1 class="display-4 mb-3">
                                <i class="fas fa-shield-alt text-warning me-3"></i>
                                مرحباً بك في نظام أرشفة اليومية العسكرية
                            </h1>
                            <p class="lead">نظام شامل لإدارة وأرشفة يوميات طلاب الدورات العسكرية بكفاءة واحترافية</p>
                        </div>
                        <div class="col-md-4 text-center">
                            <div class="military-emblem">
                                <i class="fas fa-medal"></i>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- Statistics Cards -->
                <div class="row mb-4">
                    <div class="col-lg-3 col-md-6 mb-3">
                        <div class="stat-card stat-card-primary">
                            <div class="stat-icon">
                                <i class="fas fa-graduation-cap"></i>
                            </div>
                            <div class="stat-content">
                                <h3 id="totalCourses">0</h3>
                                <p>إجمالي الدورات</p>
                            </div>
                        </div>
                    </div>
                    
                    <div class="col-lg-3 col-md-6 mb-3">
                        <div class="stat-card stat-card-success">
                            <div class="stat-icon">
                                <i class="fas fa-users"></i>
                            </div>
                            <div class="stat-content">
                                <h3 id="totalStudents">0</h3>
                                <p>إجمالي الطلاب</p>
                            </div>
                        </div>
                    </div>
                    
                    <div class="col-lg-3 col-md-6 mb-3">
                        <div class="stat-card stat-card-warning">
                            <div class="stat-icon">
                                <i class="fas fa-clipboard-list"></i>
                            </div>
                            <div class="stat-content">
                                <h3 id="totalReports">0</h3>
                                <p>إجمالي اليوميات</p>
                            </div>
                        </div>
                    </div>
                    
                    <div class="col-lg-3 col-md-6 mb-3">
                        <div class="stat-card stat-card-danger">
                            <div class="stat-icon">
                                <i class="fas fa-exclamation-triangle"></i>
                            </div>
                            <div class="stat-content">
                                <h3 id="totalViolations">0</h3>
                                <p>إجمالي المخالفات</p>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- Quick Actions -->
                <div class="row">
                    <div class="col-12">
                        <div class="quick-actions-card">
                            <h4 class="mb-4">
                                <i class="fas fa-bolt text-warning me-2"></i>
                                الإجراءات السريعة
                            </h4>
                            <div class="row">
                                <div class="col-lg-4 col-md-6 mb-3">
                                    <button class="btn btn-quick-action w-100" onclick="showSection('daily-report')">
                                        <i class="fas fa-plus-circle"></i>
                                        <span>إضافة يومية جديدة</span>
                                    </button>
                                </div>
                                <div class="col-lg-4 col-md-6 mb-3">
                                    <button class="btn btn-quick-action w-100" onclick="showSection('courses')">
                                        <i class="fas fa-graduation-cap"></i>
                                        <span>إدارة الدورات</span>
                                    </button>
                                </div>
                                <div class="col-lg-4 col-md-6 mb-3">
                                    <button class="btn btn-quick-action w-100" onclick="showSection('reports-history')">
                                        <i class="fas fa-search"></i>
                                        <span>البحث في اليوميات</span>
                                    </button>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </section>

        <!-- Courses Management Section -->
        <section id="courses" class="content-section">
            <div class="container-fluid">
                <!-- Page Header -->
                <div class="page-header mb-4">
                    <div class="row align-items-center">
                        <div class="col-md-8">
                            <h2 class="page-title">
                                <i class="fas fa-graduation-cap text-military-accent me-3"></i>
                                إدارة الدورات العسكرية
                            </h2>
                            <p class="page-subtitle">إضافة وإدارة بيانات الدورات التدريبية</p>
                        </div>
                        <div class="col-md-4 text-end">
                            <button class="btn btn-military-primary" onclick="showAddCourseModal()">
                                <i class="fas fa-plus me-2"></i>إضافة دورة جديدة
                            </button>
                        </div>
                    </div>
                </div>

                <!-- Courses Statistics -->
                <div class="row mb-4">
                    <div class="col-lg-3 col-md-6 mb-3">
                        <div class="stat-card stat-card-primary">
                            <div class="stat-icon">
                                <i class="fas fa-graduation-cap"></i>
                            </div>
                            <div class="stat-content">
                                <h3 id="activeCourses">0</h3>
                                <p>الدورات النشطة</p>
                            </div>
                        </div>
                    </div>
                    <div class="col-lg-3 col-md-6 mb-3">
                        <div class="stat-card stat-card-success">
                            <div class="stat-icon">
                                <i class="fas fa-check-circle"></i>
                            </div>
                            <div class="stat-content">
                                <h3 id="completedCourses">0</h3>
                                <p>الدورات المكتملة</p>
                            </div>
                        </div>
                    </div>
                    <div class="col-lg-3 col-md-6 mb-3">
                        <div class="stat-card stat-card-warning">
                            <div class="stat-icon">
                                <i class="fas fa-clock"></i>
                            </div>
                            <div class="stat-content">
                                <h3 id="upcomingCourses">0</h3>
                                <p>الدورات القادمة</p>
                            </div>
                        </div>
                    </div>
                    <div class="col-lg-3 col-md-6 mb-3">
                        <div class="stat-card stat-card-info">
                            <div class="stat-icon">
                                <i class="fas fa-users"></i>
                            </div>
                            <div class="stat-content">
                                <h3 id="totalEnrolled">0</h3>
                                <p>إجمالي المسجلين</p>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- Courses Table -->
                <div class="card">
                    <div class="card-header">
                        <div class="row align-items-center">
                            <div class="col-md-6">
                                <h5 class="card-title mb-0">
                                    <i class="fas fa-list me-2"></i>قائمة الدورات
                                </h5>
                            </div>
                            <div class="col-md-6">
                                <div class="input-group">
                                    <input type="text" class="form-control" id="courseSearchInput"
                                           placeholder="البحث في الدورات...">
                                    <button class="btn btn-outline-secondary" type="button">
                                        <i class="fas fa-search"></i>
                                    </button>
                                </div>
                            </div>
                        </div>
                    </div>
                    <div class="card-body">
                        <div class="table-responsive">
                            <table class="table table-hover" id="coursesTable">
                                <thead class="table-dark">
                                    <tr>
                                        <th>رمز الدورة</th>
                                        <th>رقم الدورة</th>
                                        <th>اسم الدورة</th>
                                        <th>تاريخ البداية</th>
                                        <th>تاريخ النهاية</th>
                                        <th>الحالة</th>
                                        <th>عدد الطلاب</th>
                                        <th>الإجراءات</th>
                                    </tr>
                                </thead>
                                <tbody id="coursesTableBody">
                                    <!-- سيتم ملء البيانات ديناميكياً -->
                                </tbody>
                            </table>
                        </div>
                    </div>
                </div>
            </div>
        </section>

        <!-- Daily Report Section -->
        <section id="daily-report" class="content-section">
            <div class="container-fluid">
                <!-- Page Header -->
                <div class="page-header mb-4">
                    <div class="row align-items-center">
                        <div class="col-md-8">
                            <h2 class="page-title">
                                <i class="fas fa-clipboard-list text-military-accent me-3"></i>
                                إضافة اليومية
                            </h2>
                            <p class="page-subtitle">تسجيل يومية طلاب الدورة العسكرية</p>
                        </div>
                        <div class="col-md-4 text-end">
                            <button class="btn btn-military-accent" onclick="resetDailyReport()">
                                <i class="fas fa-refresh me-2"></i>إعادة تعيين
                            </button>
                        </div>
                    </div>
                </div>

                <!-- Daily Report Form -->
                <div class="row">
                    <!-- Report Info Card -->
                    <div class="col-lg-4 mb-4">
                        <div class="card">
                            <div class="card-header">
                                <h5 class="card-title">
                                    <i class="fas fa-info-circle me-2"></i>معلومات اليومية
                                </h5>
                            </div>
                            <div class="card-body">
                                <form id="dailyReportInfoForm">
                                    <div class="mb-3">
                                        <label for="reportDate" class="form-label">
                                            <i class="fas fa-calendar me-1"></i>تاريخ اليومية *
                                        </label>
                                        <input type="date" class="form-control" id="reportDate" required>
                                    </div>

                                    <div class="mb-3">
                                        <label for="supervisorName" class="form-label">
                                            <i class="fas fa-user-tie me-1"></i>اسم المشرف *
                                        </label>
                                        <input type="text" class="form-control" id="supervisorName" required
                                               placeholder="اسم المشرف المسؤول">
                                    </div>

                                    <div class="mb-3">
                                        <label for="selectedCourse" class="form-label">
                                            <i class="fas fa-graduation-cap me-1"></i>الدورة *
                                        </label>
                                        <select class="form-select" id="selectedCourse" required onchange="loadCourseStudents()">
                                            <option value="">اختر الدورة...</option>
                                        </select>
                                    </div>

                                    <div class="mb-3">
                                        <label for="reportNotes" class="form-label">
                                            <i class="fas fa-sticky-note me-1"></i>ملاحظات عامة
                                        </label>
                                        <textarea class="form-control" id="reportNotes" rows="3"
                                                  placeholder="ملاحظات عامة على اليومية..."></textarea>
                                    </div>
                                </form>
                            </div>
                        </div>
                    </div>

                    <!-- Students List Card -->
                    <div class="col-lg-8 mb-4">
                        <div class="card">
                            <div class="card-header">
                                <div class="row align-items-center">
                                    <div class="col-md-6">
                                        <h5 class="card-title">
                                            <i class="fas fa-users me-2"></i>قائمة الطلاب
                                        </h5>
                                    </div>
                                    <div class="col-md-6 text-end">
                                        <button class="btn btn-sm btn-military-primary" onclick="showAddStudentModal()">
                                            <i class="fas fa-user-plus me-1"></i>إضافة طالب
                                        </button>
                                    </div>
                                </div>
                            </div>
                            <div class="card-body">
                                <div id="studentsContainer">
                                    <div class="text-center text-muted py-5">
                                        <i class="fas fa-users fa-3x mb-3 d-block"></i>
                                        <p>يرجى اختيار الدورة أولاً لعرض قائمة الطلاب</p>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- Attachments Section -->
                <div class="row">
                    <div class="col-12">
                        <div class="card">
                            <div class="card-header">
                                <div class="row align-items-center">
                                    <div class="col-md-6">
                                        <h5 class="card-title">
                                            <i class="fas fa-paperclip me-2"></i>المرفقات والمستندات
                                        </h5>
                                    </div>
                                    <div class="col-md-6 text-end">
                                        <button class="btn btn-sm btn-military-accent" onclick="showAttachmentModal()">
                                            <i class="fas fa-upload me-1"></i>إضافة مرفق
                                        </button>
                                        <button class="btn btn-sm btn-outline-primary" onclick="startScanning()">
                                            <i class="fas fa-scanner me-1"></i>مسح ضوئي
                                        </button>
                                    </div>
                                </div>
                            </div>
                            <div class="card-body">
                                <div id="attachmentsContainer">
                                    <div class="text-center text-muted py-4">
                                        <i class="fas fa-file-upload fa-2x mb-2 d-block"></i>
                                        <p>لا توجد مرفقات حالياً</p>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- Save Report Button -->
                <div class="row mt-4">
                    <div class="col-12 text-center">
                        <button class="btn btn-military-primary btn-lg px-5" onclick="saveDailyReport()">
                            <i class="fas fa-save me-2"></i>حفظ اليومية
                        </button>
                    </div>
                </div>
            </div>
        </section>

        <section id="reports-history" class="content-section">
            <div class="container-fluid">
                <h2>سجل اليوميات</h2>
                <p>قريباً...</p>
            </div>
        </section>

        <section id="users" class="content-section">
            <div class="container-fluid">
                <h2>إدارة المستخدمين</h2>
                <p>قريباً...</p>
            </div>
        </section>

        <section id="settings" class="content-section">
            <div class="container-fluid">
                <h2>الإعدادات</h2>
                <p>قريباً...</p>
            </div>
        </section>
    </main>

    <!-- Attachment Modal -->
    <div class="modal fade" id="attachmentModal" tabindex="-1" aria-labelledby="attachmentModalLabel" aria-hidden="true">
        <div class="modal-dialog modal-lg">
            <div class="modal-content">
                <div class="modal-header bg-military-accent text-white">
                    <h5 class="modal-title" id="attachmentModalLabel">
                        <i class="fas fa-paperclip me-2"></i>إضافة مرفق
                    </h5>
                    <button type="button" class="btn-close btn-close-white" data-bs-dismiss="modal" aria-label="إغلاق"></button>
                </div>
                <div class="modal-body">
                    <div class="row">
                        <div class="col-md-6">
                            <h6 class="mb-3">
                                <i class="fas fa-upload me-2"></i>رفع ملف
                            </h6>
                            <div class="drop-zone" id="dropZone" onclick="document.getElementById('fileInput').click()">
                                <i class="fas fa-cloud-upload-alt"></i>
                                <p class="mb-2">اسحب الملفات هنا أو اضغط للاختيار</p>
                                <small class="text-muted">PDF, JPG, PNG, DOC, DOCX (حد أقصى 10 ميجابايت)</small>
                            </div>
                            <input type="file" id="fileInput" class="d-none" multiple
                                   accept=".pdf,.jpg,.jpeg,.png,.doc,.docx" onchange="handleFileSelect(this.files)">
                        </div>
                        <div class="col-md-6">
                            <h6 class="mb-3">
                                <i class="fas fa-scanner me-2"></i>مسح ضوئي
                            </h6>
                            <div class="text-center">
                                <button class="btn btn-outline-primary mb-3" onclick="startWebcamScan()">
                                    <i class="fas fa-camera me-2"></i>مسح بالكاميرا
                                </button>
                                <br>
                                <button class="btn btn-outline-secondary" onclick="startScannerScan()">
                                    <i class="fas fa-print me-2"></i>مسح بالماسح الضوئي
                                </button>
                                <div id="scannerStatus" class="mt-3 text-muted">
                                    <small>جاري البحث عن الماسحات الضوئية المتاحة...</small>
                                </div>
                            </div>
                        </div>
                    </div>

                    <hr>

                    <div class="row">
                        <div class="col-12">
                            <h6 class="mb-3">
                                <i class="fas fa-info-circle me-2"></i>معلومات المرفق
                            </h6>
                            <form id="attachmentForm">
                                <div class="row">
                                    <div class="col-md-6 mb-3">
                                        <label for="attachmentName" class="form-label">اسم المرفق *</label>
                                        <input type="text" class="form-control" id="attachmentName" required
                                               placeholder="مثال: إجازة مرضية - أحمد محمد">
                                    </div>
                                    <div class="col-md-6 mb-3">
                                        <label for="attachmentType" class="form-label">نوع المرفق *</label>
                                        <select class="form-select" id="attachmentType" required>
                                            <option value="">اختر نوع المرفق...</option>
                                            <option value="medical_leave">إجازة مرضية</option>
                                            <option value="death_certificate">شهادة وفاة</option>
                                            <option value="birth_certificate">شهادة ميلاد</option>
                                            <option value="official_document">وثيقة رسمية</option>
                                            <option value="excuse_letter">خطاب عذر</option>
                                            <option value="other">أخرى</option>
                                        </select>
                                    </div>
                                </div>
                                <div class="mb-3">
                                    <label for="attachmentDescription" class="form-label">وصف المرفق</label>
                                    <textarea class="form-control" id="attachmentDescription" rows="2"
                                              placeholder="وصف مختصر للمرفق..."></textarea>
                                </div>
                                <div class="row">
                                    <div class="col-md-6 mb-3">
                                        <label for="relatedStudent" class="form-label">الطالب المرتبط</label>
                                        <select class="form-select" id="relatedStudent">
                                            <option value="">اختر الطالب...</option>
                                        </select>
                                    </div>
                                    <div class="col-md-6 mb-3">
                                        <label for="attachmentDate" class="form-label">تاريخ المرفق</label>
                                        <input type="date" class="form-control" id="attachmentDate">
                                    </div>
                                </div>
                            </form>
                        </div>
                    </div>

                    <!-- Preview Area -->
                    <div id="previewArea" class="mt-3" style="display: none;">
                        <h6 class="mb-3">
                            <i class="fas fa-eye me-2"></i>معاينة المرفق
                        </h6>
                        <div id="previewContent" class="text-center">
                            <!-- سيتم إدراج المعاينة هنا -->
                        </div>
                    </div>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">
                        <i class="fas fa-times me-1"></i>إلغاء
                    </button>
                    <button type="button" class="btn btn-military-accent" onclick="saveAttachment()">
                        <i class="fas fa-save me-1"></i>حفظ المرفق
                    </button>
                </div>
            </div>
        </div>
    </div>

    <!-- Add Student Modal -->
    <div class="modal fade" id="addStudentModal" tabindex="-1" aria-labelledby="addStudentModalLabel" aria-hidden="true">
        <div class="modal-dialog modal-lg">
            <div class="modal-content">
                <div class="modal-header bg-military-primary text-white">
                    <h5 class="modal-title" id="addStudentModalLabel">
                        <i class="fas fa-user-plus me-2"></i>إضافة طالب جديد
                    </h5>
                    <button type="button" class="btn-close btn-close-white" data-bs-dismiss="modal" aria-label="إغلاق"></button>
                </div>
                <div class="modal-body">
                    <form id="addStudentForm">
                        <div class="row">
                            <div class="col-md-6 mb-3">
                                <label for="studentName" class="form-label">
                                    <i class="fas fa-user me-1"></i>الاسم الكامل *
                                </label>
                                <input type="text" class="form-control" id="studentName" required
                                       placeholder="الاسم الثلاثي كاملاً">
                            </div>
                            <div class="col-md-6 mb-3">
                                <label for="militaryNumber" class="form-label">
                                    <i class="fas fa-id-card me-1"></i>الرقم العسكري *
                                </label>
                                <input type="text" class="form-control" id="militaryNumber" required
                                       placeholder="مثال: MIL123456">
                            </div>
                        </div>

                        <div class="row">
                            <div class="col-md-6 mb-3">
                                <label for="studentRank" class="form-label">
                                    <i class="fas fa-star me-1"></i>الرتبة *
                                </label>
                                <select class="form-select" id="studentRank" required>
                                    <option value="">اختر الرتبة...</option>
                                    <option value="جندي">جندي</option>
                                    <option value="جندي أول">جندي أول</option>
                                    <option value="عريف">عريف</option>
                                    <option value="رقيب">رقيب</option>
                                    <option value="رقيب أول">رقيب أول</option>
                                    <option value="رئيس رقباء">رئيس رقباء</option>
                                    <option value="ملازم">ملازم</option>
                                    <option value="ملازم أول">ملازم أول</option>
                                    <option value="نقيب">نقيب</option>
                                    <option value="رائد">رائد</option>
                                    <option value="مقدم">مقدم</option>
                                    <option value="عقيد">عقيد</option>
                                    <option value="عميد">عميد</option>
                                </select>
                            </div>
                            <div class="col-md-6 mb-3">
                                <label for="studentCourse" class="form-label">
                                    <i class="fas fa-graduation-cap me-1"></i>الدورة *
                                </label>
                                <select class="form-select" id="studentCourse" required onchange="updateCourseInfo()">
                                    <option value="">اختر الدورة...</option>
                                </select>
                            </div>
                        </div>

                        <div class="row">
                            <div class="col-md-6 mb-3">
                                <label for="studentCourseNumber" class="form-label">
                                    <i class="fas fa-hashtag me-1"></i>رقم الدورة
                                </label>
                                <input type="text" class="form-control" id="studentCourseNumber" readonly>
                            </div>
                            <div class="col-md-6 mb-3">
                                <label for="studentCourseName" class="form-label">
                                    <i class="fas fa-book me-1"></i>اسم الدورة
                                </label>
                                <input type="text" class="form-control" id="studentCourseName" readonly>
                            </div>
                        </div>
                    </form>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">
                        <i class="fas fa-times me-1"></i>إلغاء
                    </button>
                    <button type="button" class="btn btn-military-primary" onclick="saveStudent()">
                        <i class="fas fa-save me-1"></i>حفظ الطالب
                    </button>
                </div>
            </div>
        </div>
    </div>

    <!-- Add Course Modal -->
    <div class="modal fade" id="addCourseModal" tabindex="-1" aria-labelledby="addCourseModalLabel" aria-hidden="true">
        <div class="modal-dialog modal-lg">
            <div class="modal-content">
                <div class="modal-header bg-military-primary text-white">
                    <h5 class="modal-title" id="addCourseModalLabel">
                        <i class="fas fa-plus-circle me-2"></i>إضافة دورة جديدة
                    </h5>
                    <button type="button" class="btn-close btn-close-white" data-bs-dismiss="modal" aria-label="إغلاق"></button>
                </div>
                <div class="modal-body">
                    <form id="addCourseForm">
                        <div class="row">
                            <div class="col-md-6 mb-3">
                                <label for="courseCode" class="form-label">
                                    <i class="fas fa-code me-1"></i>رمز الدورة *
                                </label>
                                <input type="text" class="form-control" id="courseCode" required
                                       placeholder="مثال: MIL001">
                                <div class="form-text">رمز فريد للدورة (مثل: MIL001, OFF001)</div>
                            </div>
                            <div class="col-md-6 mb-3">
                                <label for="courseNumber" class="form-label">
                                    <i class="fas fa-hashtag me-1"></i>رقم الدورة *
                                </label>
                                <input type="text" class="form-control" id="courseNumber" required
                                       placeholder="مثال: 2024-01">
                                <div class="form-text">رقم الدورة للسنة الحالية</div>
                            </div>
                        </div>

                        <div class="mb-3">
                            <label for="courseName" class="form-label">
                                <i class="fas fa-graduation-cap me-1"></i>اسم الدورة *
                            </label>
                            <input type="text" class="form-control" id="courseName" required
                                   placeholder="مثال: دورة الضباط الأساسية">
                        </div>

                        <div class="row">
                            <div class="col-md-6 mb-3">
                                <label for="startDate" class="form-label">
                                    <i class="fas fa-calendar-alt me-1"></i>تاريخ بداية الدورة *
                                </label>
                                <input type="date" class="form-control" id="startDate" required>
                            </div>
                            <div class="col-md-6 mb-3">
                                <label for="endDate" class="form-label">
                                    <i class="fas fa-calendar-check me-1"></i>تاريخ نهاية الدورة *
                                </label>
                                <input type="date" class="form-control" id="endDate" required>
                            </div>
                        </div>

                        <div class="mb-3">
                            <label for="courseDescription" class="form-label">
                                <i class="fas fa-align-left me-1"></i>وصف الدورة
                            </label>
                            <textarea class="form-control" id="courseDescription" rows="3"
                                      placeholder="وصف مختصر عن محتوى ومدة الدورة..."></textarea>
                        </div>

                        <div class="row">
                            <div class="col-md-6 mb-3">
                                <label for="maxStudents" class="form-label">
                                    <i class="fas fa-users me-1"></i>الحد الأقصى للطلاب
                                </label>
                                <input type="number" class="form-control" id="maxStudents" min="1" max="1000"
                                       placeholder="50">
                            </div>
                            <div class="col-md-6 mb-3">
                                <label for="courseStatus" class="form-label">
                                    <i class="fas fa-flag me-1"></i>حالة الدورة
                                </label>
                                <select class="form-select" id="courseStatus">
                                    <option value="upcoming">قادمة</option>
                                    <option value="active" selected>نشطة</option>
                                    <option value="completed">مكتملة</option>
                                    <option value="cancelled">ملغية</option>
                                </select>
                            </div>
                        </div>
                    </form>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">
                        <i class="fas fa-times me-1"></i>إلغاء
                    </button>
                    <button type="button" class="btn btn-military-primary" onclick="saveCourse()">
                        <i class="fas fa-save me-1"></i>حفظ الدورة
                    </button>
                </div>
            </div>
        </div>
    </div>

    <!-- Toast Notifications Container -->
    <div class="toast-container position-fixed top-0 end-0 p-3" id="toastContainer"></div>

    <!-- Bootstrap JS -->
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
    
    <!-- Custom JS -->
    <script src="assets/js/scanner.js"></script>
    <script src="assets/js/app.js"></script>
</body>
</html>
